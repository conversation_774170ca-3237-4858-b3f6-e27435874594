/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    padding: 2rem 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.header-content .subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Main Content */
.main-content {
    padding: 2rem 0;
}

/* Upload Section */
.upload-section {
    margin-bottom: 2rem;
}

.upload-card {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    border: 1px solid #e1e5e9;
}

.upload-card h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.upload-area {
    border: 2px dashed #3498db;
    border-radius: 8px;
    padding: 3rem 2rem;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #2980b9;
    background: #e3f2fd;
}

.upload-area.dragover {
    border-color: #27ae60;
    background: #e8f5e8;
}

.upload-icon {
    font-size: 3rem;
    color: #3498db;
    margin-bottom: 1rem;
}

.upload-content h3 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

.browse-link {
    color: #3498db;
    cursor: pointer;
    text-decoration: underline;
}

.browse-link:hover {
    color: #2980b9;
}

.file-info {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.upload-status {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 6px;
    display: none;
}

.upload-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    display: block;
}

.upload-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    display: block;
}

/* Statistics Section */
.stats-section {
    margin-bottom: 2rem;
}

.stats-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    font-size: 2rem;
    color: #3498db;
    width: 60px;
    text-align: center;
}

.stat-icon.text-success {
    color: #27ae60;
}

.stat-icon.text-danger {
    color: #e74c3c;
}

.stat-content h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Data Section */
.data-section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
    border: 1px solid #e1e5e9;
}

.data-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.data-header h2 {
    color: #2c3e50;
    font-size: 1.5rem;
}

.data-controls {
    display: flex;
    gap: 0.5rem;
}

/* Button Styles */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
}

.btn-secondary {
    background: #27ae60;
    color: white;
}

.btn-secondary:hover {
    background: #229954;
}

.btn-outline {
    background: transparent;
    color: #3498db;
    border: 1px solid #3498db;
}

.btn-outline:hover {
    background: #3498db;
    color: white;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Table Styles */
.table-container {
    overflow-x: auto;
    margin-bottom: 1rem;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.data-table th {
    background: #f8f9fa;
    color: #2c3e50;
    font-weight: 600;
    padding: 1rem 0.75rem;
    text-align: left;
    border-bottom: 2px solid #e1e5e9;
    white-space: nowrap;
}

.data-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #e1e5e9;
    white-space: nowrap;
}

.data-table tbody tr:hover {
    background: #f8f9fa;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 1rem;
}

.page-info {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1001;
}

.modal-content {
    background: white;
    border-radius: 8px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: #e74c3c;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #7f8c8d;
}

.close-btn:hover {
    color: #2c3e50;
}

.modal-body {
    padding: 1.5rem;
}

.modal-body p {
    white-space: pre-line;
    line-height: 1.5;
    margin: 0;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e1e5e9;
    text-align: right;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .header-content h1 {
        font-size: 2rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .data-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .data-controls {
        justify-content: center;
    }
    
    .upload-area {
        padding: 2rem 1rem;
    }
}

/* Utility Classes */
.text-success {
    color: #27ae60 !important;
}

.text-danger {
    color: #e74c3c !important;
}

.text-center {
    text-align: center;
}

.mb-1 {
    margin-bottom: 0.5rem;
}

.mb-2 {
    margin-bottom: 1rem;
}

.mb-3 {
    margin-bottom: 1.5rem;
}

/* Navigation Tabs */
.nav-tabs {
    background: white;
    border-bottom: 1px solid #e1e5e9;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    gap: 0;
}

.nav-tab {
    background: none;
    border: none;
    padding: 1rem 2rem;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    color: #7f8c8d;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
}

.nav-tab:hover {
    color: #3498db;
    background: #f8f9fa;
}

.nav-tab.active {
    color: #3498db;
    border-bottom-color: #3498db;
    background: #f8f9fa;
}

.alert-badge {
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    padding: 0.2rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 1.5rem;
    text-align: center;
}

/* View Containers */
.view-container {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Alert Summary Section */
.alert-summary-section {
    margin-bottom: 2rem;
}

.alert-summary-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.alert-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.alert-stat-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.alert-stat-icon {
    font-size: 2rem;
    width: 60px;
    text-align: center;
    border-radius: 50%;
    padding: 0.75rem;
}

.alert-stat-icon.new {
    color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
}

.alert-stat-icon.reviewed {
    color: #f39c12;
    background: rgba(243, 156, 18, 0.1);
}

.alert-stat-icon.dismissed {
    color: #95a5a6;
    background: rgba(149, 165, 166, 0.1);
}

.alert-stat-icon.total {
    color: #3498db;
    background: rgba(52, 152, 219, 0.1);
}

.alert-stat-content h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.alert-stat-content p {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Alert Controls Section */
.alert-controls-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
}

.alert-controls-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.alert-controls-header h3 {
    color: #2c3e50;
    margin: 0;
    font-size: 1.2rem;
}

.alert-actions {
    display: flex;
    gap: 0.5rem;
}

.alert-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 500;
    color: #2c3e50;
    font-size: 0.9rem;
}

.filter-select,
.filter-input {
    padding: 0.5rem;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    font-size: 0.9rem;
    background: white;
}

.filter-select:focus,
.filter-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Alerts List Section */
.alerts-list-section {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e5e9;
}

.alerts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.alerts-header h3 {
    color: #2c3e50;
    margin: 0;
    font-size: 1.2rem;
}

.bulk-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.alerts-container {
    min-height: 200px;
}

.no-alerts-message {
    text-align: center;
    padding: 3rem 2rem;
    color: #7f8c8d;
}

.no-alerts-message i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #bdc3c7;
}

.no-alerts-message h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

/* Alert Item Styles */
.alert-item {
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    margin-bottom: 1rem;
    background: white;
    transition: all 0.3s ease;
    cursor: pointer;
}

.alert-item:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-color: #3498db;
}

.alert-item.selected {
    border-color: #3498db;
    background: #f8f9fa;
}

.alert-item-header {
    padding: 1rem;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.alert-type {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.alert-type i {
    color: #e74c3c;
}

.alert-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.alert-status.new {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

.alert-status.reviewed {
    background: rgba(243, 156, 18, 0.1);
    color: #f39c12;
}

.alert-status.dismissed {
    background: rgba(149, 165, 166, 0.1);
    color: #95a5a6;
}

.alert-item-body {
    padding: 1rem;
}

.alert-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.alert-info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.alert-info-label {
    font-size: 0.8rem;
    color: #7f8c8d;
    font-weight: 500;
    text-transform: uppercase;
}

.alert-info-value {
    font-weight: 600;
    color: #2c3e50;
}

.alert-actions-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #e1e5e9;
    flex-wrap: wrap;
    gap: 1rem;
}

.alert-timestamp {
    font-size: 0.8rem;
    color: #7f8c8d;
}

.alert-item-actions {
    display: flex;
    gap: 0.5rem;
}

.alert-checkbox {
    margin-right: 0.5rem;
}

/* Alert Pagination */
.alert-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e1e5e9;
}

/* Alert Modal Styles */
.alert-modal-content {
    max-width: 800px;
    width: 95%;
}

.alert-detail-content {
    max-height: 60vh;
    overflow-y: auto;
}

.alert-detail-section {
    margin-bottom: 2rem;
}

.alert-detail-section h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    border-bottom: 1px solid #e1e5e9;
    padding-bottom: 0.5rem;
}

.transaction-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.transaction-detail-card {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    border-left: 4px solid #3498db;
}

.transaction-detail-card.debit {
    border-left-color: #e74c3c;
}

.transaction-detail-card.credit {
    border-left-color: #27ae60;
}

.transaction-detail-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #2c3e50;
}

.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    font-family: inherit;
    font-size: 0.9rem;
    resize: vertical;
}

.form-textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Responsive Design for Alerts */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 15px;
    }

    .nav-tab {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .alert-stats-grid {
        grid-template-columns: 1fr;
    }

    .alert-filters {
        grid-template-columns: 1fr;
    }

    .alert-controls-header {
        flex-direction: column;
        align-items: stretch;
    }

    .alert-actions {
        justify-content: center;
    }

    .alerts-header {
        flex-direction: column;
        align-items: stretch;
    }

    .bulk-actions {
        justify-content: center;
    }

    .alert-item-header {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .alert-info-grid {
        grid-template-columns: 1fr;
    }

    .alert-actions-row {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .alert-item-actions {
        justify-content: center;
    }

    .transaction-details-grid {
        grid-template-columns: 1fr;
    }

    .alert-modal-content {
        width: 95%;
        margin: 1rem;
    }
}

/* Additional Utility Classes for Alerts */
.text-warning {
    color: #f39c12 !important;
}

.text-muted {
    color: #7f8c8d !important;
}

.border-left-danger {
    border-left: 4px solid #e74c3c !important;
}

.border-left-success {
    border-left: 4px solid #27ae60 !important;
}

.border-left-warning {
    border-left: 4px solid #f39c12 !important;
}

.bg-light {
    background-color: #f8f9fa !important;
}

.font-weight-bold {
    font-weight: 600 !important;
}

.small {
    font-size: 0.8rem !important;
}

/* Animation for alert notifications */
@keyframes alertPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.alert-badge.pulse {
    animation: alertPulse 1s ease-in-out infinite;
}

/* Loading state for alerts */
.alerts-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
    color: #7f8c8d;
}

.alerts-loading i {
    margin-right: 0.5rem;
    animation: spin 1s linear infinite;
}

/* Alert Notes Styles */
.alert-notes {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.alert-note {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    border-left: 4px solid #3498db;
}

.alert-note-content {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.alert-note-timestamp {
    font-size: 0.8rem;
    color: #7f8c8d;
    font-style: italic;
}

/* Enhanced button styles for alerts */
.btn-sm.btn-primary {
    background: #3498db;
    border-color: #3498db;
}

.btn-sm.btn-primary:hover {
    background: #2980b9;
    border-color: #2980b9;
}

.btn-sm.btn-secondary {
    background: #27ae60;
    border-color: #27ae60;
}

.btn-sm.btn-secondary:hover {
    background: #229954;
    border-color: #229954;
}

.btn-sm.btn-outline {
    background: transparent;
    color: #7f8c8d;
    border-color: #7f8c8d;
}

.btn-sm.btn-outline:hover {
    background: #7f8c8d;
    color: white;
    border-color: #7f8c8d;
}

/* Alert item hover effects */
.alert-item:hover .alert-item-actions .btn {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Checkbox styling */
.alert-checkbox {
    width: 1.2rem;
    height: 1.2rem;
    cursor: pointer;
    accent-color: #3498db;
}

/* Status badge animations */
.alert-status {
    transition: all 0.3s ease;
}

.alert-status.new {
    animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Enhanced modal styles for alerts */
.alert-modal-content .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

.alert-modal-content .modal-footer {
    border-top: 1px solid #e1e5e9;
    background: #f8f9fa;
}

/* Transaction detail cards enhancement */
.transaction-detail-card {
    transition: all 0.3s ease;
}

.transaction-detail-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Filter section enhancements */
.filter-group input:focus,
.filter-group select:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(52, 152, 219, 0.2);
}

/* Alert statistics cards hover effect */
.alert-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

/* Navigation tab enhancements */
.nav-tab {
    position: relative;
    overflow: hidden;
}

.nav-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.1), transparent);
    transition: left 0.5s ease;
}

.nav-tab:hover::before {
    left: 100%;
}

/* Improved scrollbar for modal content */
.alert-detail-content::-webkit-scrollbar {
    width: 6px;
}

.alert-detail-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.alert-detail-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.alert-detail-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
