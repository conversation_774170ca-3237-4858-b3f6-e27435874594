<!DOCTYPE html>
<html>
<head>
    <title>Create Test Excel File</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <h1>Create Test Excel File</h1>
    <button onclick="createTestFile()">Create Test Excel File</button>
    
    <script>
        function createTestFile() {
            const testData = [
                ['Transaction ID', 'Trans Ref No', 'Customer Id', 'Customer Name', 'Account No', 'Account Open Date', 'Product Type', 'Branch', 'Date', 'Tran Amount', 'Tran Currency', 'Dr or Cr', 'Counter Party Name', 'Counter Customer ID', 'Counter Account No.', 'Remarks', 'Particulars', 'Transaction Location Id', 'Approved User Id', 'Entry User Id', 'Posted User Id'],
                ['TXN001', 'REF001', 'CUST001', '<PERSON>', 'ACC001', '2023-01-01', 'Savings', 'Branch A', '2024-01-15', 500000.00, 'USD', 'Dr', 'ABC Corp', 'CUST002', 'ACC002', 'High-value payment', 'Service payment', 'LOC001', 'USER001', 'USER002', 'USER003'],
                ['TXN002', 'REF002', 'CUST001', 'John Smith', 'ACC001', '2023-01-01', 'Savings', 'Branch A', '2024-01-16', 500000.00, 'USD', 'Cr', 'ABC Corp', 'CUST003', 'ACC003', 'High-value refund', 'Refund payment', 'LOC001', 'USER001', 'USER002', 'USER003'],
                ['TXN003', 'REF003', 'CUST002', 'Jane Doe', 'ACC004', '2023-02-01', 'Checking', 'Branch B', '2024-01-16', 750000.00, 'USD', 'Dr', 'DEF Inc', 'CUST004', 'ACC005', 'High-value purchase', 'Purchase', 'LOC002', 'USER004', 'USER005', 'USER006'],
                ['TXN004', 'REF004', 'CUST002', 'Jane Doe', 'ACC004', '2023-02-01', 'Checking', 'Branch B', '2024-01-17', 750000.00, 'USD', 'Cr', 'DEF Inc', 'CUST005', 'ACC006', 'High-value refund', 'Refund', 'LOC002', 'USER004', 'USER005', 'USER006'],
                ['TXN005', 'REF005', 'CUST003', 'Bob Johnson', 'ACC007', '2023-03-01', 'Savings', 'Branch C', '2024-01-17', 400000.00, 'USD', 'Dr', 'JKL Ltd', 'CUST006', 'ACC008', 'High-value investment', 'Investment payment', 'LOC003', 'USER007', 'USER008', 'USER009'],
                ['TXN006', 'REF006', 'CUST003', 'Bob Johnson', 'ACC007', '2023-03-01', 'Savings', 'Branch C', '2024-01-18', 400000.00, 'USD', 'Cr', 'JKL Ltd', 'CUST007', 'ACC009', 'High-value dividend', 'Dividend payment', 'LOC003', 'USER007', 'USER008', 'USER009'],
                ['TXN007', 'REF007', 'CUST004', 'Alice Brown', 'ACC010', '2023-04-01', 'Checking', 'Branch D', '2024-01-18', 750.00, 'USD', 'Dr', 'PQR Corp', 'CUST008', 'ACC011', 'Loan payment', 'Loan', 'LOC004', 'USER010', 'USER011', 'USER012'],
                ['TXN008', 'REF008', 'CUST005', 'Charlie Wilson', 'ACC013', '2023-05-01', 'Savings', 'Branch E', '2024-01-19', 300.00, 'USD', 'Cr', 'STU Ltd', 'CUST009', 'ACC014', 'Interest credit', 'Interest', 'LOC005', 'USER013', 'USER014', 'USER015'],
                ['TXN009', 'REF009', 'CUST001', 'John Smith', 'ACC001', '2023-01-01', 'Savings', 'Branch A', '2024-01-20', 250.00, 'USD', 'Dr', 'VWX Inc', 'CUST010', 'ACC016', 'Utility payment', 'Utilities', 'LOC001', 'USER001', 'USER002', 'USER003'],
                ['TXN010', 'REF010', 'CUST001', 'John Smith', 'ACC001', '2023-01-01', 'Savings', 'Branch A', '2024-01-20', 250.00, 'USD', 'Cr', 'YZA Corp', 'CUST011', 'ACC017', 'Cashback', 'Cashback reward', 'LOC001', 'USER001', 'USER002', 'USER003']
            ];

            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet(testData);
            XLSX.utils.book_append_sheet(wb, ws, 'Transactions');
            XLSX.writeFile(wb, 'test-alert-data.xlsx');
            
            alert('Test Excel file created successfully!');
        }
    </script>
</body>
</html>
