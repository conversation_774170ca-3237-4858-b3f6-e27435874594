# Transaction Analysis Dashboard

A professional, client-side web application for analyzing banking transaction data from Excel files.

## Features

### Core Functionality
- **Excel File Upload**: Drag-and-drop or browse to select .xlsx/.xls files
- **Data Validation**: Validates all 21 required columns and data types
- **Real-time Processing**: Client-side processing with no server dependencies
- **Professional UI**: Banking-grade interface with responsive design
- **Data Export**: Export processed data back to Excel format

### Required Excel Columns (in exact order)
1. Transaction ID
2. Trans Ref No
3. Customer Id
4. Customer Name
5. Account No
6. Account Open Date
7. Product Type
8. Branch
9. Date
10. Tran Amount
11. Tran Currency
12. Dr or Cr
13. Counter Party Name
14. Counter Customer ID
15. Counter Account No.
16. Remarks
17. Particulars
18. Transaction Location Id
19. Approved User Id
20. Entry User Id
21. Posted User Id

### Dashboard Components
- **Upload Section**: File upload with drag-and-drop support
- **Statistics Summary**: 
  - Total transaction count
  - Total credits and debits
  - Net amount calculation
- **Data Preview**: Paginated table view of transaction data
- **Export Functionality**: Download processed data as Excel file

## Technical Specifications

### Technologies Used
- **HTML5**: Semantic markup and structure
- **CSS3**: Professional styling with responsive design
- **Vanilla JavaScript**: Core functionality without frameworks
- **SheetJS (XLSX)**: Excel file parsing via CDN
- **Font Awesome**: Professional icons via CDN

### Browser Compatibility
- Chrome (recommended)
- Firefox
- Safari
- Microsoft Edge

### File Requirements
- **Supported formats**: .xlsx, .xls
- **Maximum file size**: 50MB
- **Required structure**: First row must contain exact column headers
- **Data validation**: Automatic validation for dates, amounts, and Dr/Cr fields

## Usage Instructions

### 1. Upload Excel File
- Click the upload area or drag and drop an Excel file
- File must contain all 21 required columns in the specified order
- System will validate file format and structure

### 2. Review Data
- View transaction statistics in the summary cards
- Browse transaction data in the paginated table
- Use pagination controls to navigate through large datasets

### 3. Export Data
- Click "Export Data" to download processed transactions
- Exported file includes all validated and formatted data
- Filename includes timestamp for easy identification

### 4. Clear Data
- Use "Clear Data" button to reset the dashboard
- Confirmation dialog prevents accidental data loss

## Data Validation

### Automatic Validation
- **Amounts**: Removes currency symbols, validates numeric values
- **Dates**: Handles Excel date formats and standard date strings
- **Dr/Cr**: Normalizes debit/credit indicators
- **Missing Data**: Handles empty cells gracefully

### Error Handling
- **File Format**: Validates Excel file types
- **Column Structure**: Ensures all required columns are present
- **Data Quality**: Provides warnings for invalid data
- **User Feedback**: Clear error messages and status updates

## Security Features
- **Client-side Processing**: No data transmitted to external servers
- **File Size Limits**: Prevents processing of excessively large files
- **Input Validation**: Comprehensive validation of all user inputs
- **Error Boundaries**: Graceful handling of processing errors

## Keyboard Shortcuts
- **Escape**: Close error dialogs
- **Ctrl+E**: Export data (when available)

## File Structure
```
Transaction Analysis Dashboard/
├── index.html          # Main application structure
├── styles.css          # Professional styling
├── script.js           # Core functionality
└── README.md          # Documentation
```

## Sample Data Format

Your Excel file should have the following structure:

| Transaction ID | Trans Ref No | Customer Id | Customer Name | Account No | ... |
|---------------|--------------|-------------|---------------|------------|-----|
| TXN001        | REF001       | CUST001     | John Doe      | ACC001     | ... |
| TXN002        | REF002       | CUST002     | Jane Smith    | ACC002     | ... |

## Troubleshooting

### Common Issues
1. **"Missing required columns" error**: Ensure Excel file has all 21 columns in exact order
2. **"Invalid file format" error**: Use only .xlsx or .xls files
3. **"File too large" error**: Reduce file size to under 50MB
4. **Loading issues**: Ensure stable internet connection for CDN resources

### Performance Tips
- For large files (>10,000 rows), processing may take a few seconds
- Use modern browsers for optimal performance
- Close other browser tabs if experiencing memory issues

## Support
This application runs entirely in your browser and does not collect or transmit any data. All processing is performed locally on your device for maximum security and privacy.

## Version
Version 1.0 - Professional Transaction Analysis Dashboard
