# Transaction Analysis Dashboard with Alert Management System

A professional, client-side web application for analyzing banking transaction data from Excel files with comprehensive alert management capabilities.

## Features

### Core Functionality
- **Excel File Upload**: Drag-and-drop or browse to select .xlsx/.xls files
- **Data Validation**: Validates all 21 required columns and data types
- **Real-time Processing**: Client-side processing with no server dependencies
- **Professional UI**: Banking-grade interface with responsive design
- **Data Export**: Export processed data back to Excel format

### Alert Management System
- **Two-Day High-Value Debit/Credit Detection**: Automatically identifies suspicious high-value debit and credit transactions (≥300,000) with identical amounts and matching counter parties within a two-day period
- **Consolidated Alert Approach**: Groups all suspicious transaction pairs by customer and two-day period to reduce alert noise
- **Alert Dashboard**: Comprehensive alert management interface with statistics and filtering
- **Alert Status Management**: Mark alerts as new, reviewed, or dismissed
- **Alert Notes**: Add comments and notes to alerts for investigation tracking
- **Bulk Operations**: Perform actions on multiple alerts simultaneously
- **Alert Export**: Export alerts to Excel/CSV format for reporting
- **Real-time Notifications**: Alert badge notifications for new alerts

### Required Excel Columns (in exact order)
1. Transaction ID
2. Trans Ref No
3. Customer Id
4. Customer Name
5. Account No
6. Account Open Date
7. Product Type
8. Branch
9. Date
10. Tran Amount
11. Tran Currency
12. Dr or Cr
13. Counter Party Name
14. Counter Customer ID
15. Counter Account No.
16. Remarks
17. Particulars
18. Transaction Location Id
19. Approved User Id
20. Entry User Id
21. Posted User Id

### Dashboard Components
- **Tabbed Navigation**: Switch between transaction analysis and alert management
- **Upload Section**: File upload with drag-and-drop support
- **Statistics Summary**:
  - Total transaction count
  - Total credits and debits
  - Net amount calculation
  - Alert counts by status
- **Data Preview**: Paginated table view of transaction data
- **Alert Management**: Comprehensive alert dashboard with filtering and actions
- **Export Functionality**: Download processed data and alerts as Excel files

## Technical Specifications

### Technologies Used
- **HTML5**: Semantic markup and structure
- **CSS3**: Professional styling with responsive design
- **Vanilla JavaScript**: Core functionality without frameworks
- **SheetJS (XLSX)**: Excel file parsing via CDN
- **Font Awesome**: Professional icons via CDN

### Browser Compatibility
- Chrome (recommended)
- Firefox
- Safari
- Microsoft Edge

### File Requirements
- **Supported formats**: .xlsx, .xls
- **Maximum file size**: 50MB
- **Required structure**: First row must contain exact column headers
- **Data validation**: Automatic validation for dates, amounts, and Dr/Cr fields

## Usage Instructions

### 1. Upload Excel File
- Click the upload area or drag and drop an Excel file
- File must contain all 21 required columns in the specified order
- System will validate file format and structure

### 2. Review Data
- View transaction statistics in the summary cards
- Browse transaction data in the paginated table
- Use pagination controls to navigate through large datasets

### 3. Export Data
- Click "Export Data" to download processed transactions
- Exported file includes all validated and formatted data
- Filename includes timestamp for easy identification

### 4. Alert Management
- Click the "Alerts" tab to access the alert management dashboard
- Review alert statistics and filter alerts by status, date, or customer
- Click "View Details" to see comprehensive alert information
- Mark alerts as reviewed or dismissed
- Add investigation notes to alerts
- Export alerts for compliance reporting

### 5. Clear Data
- Use "Clear Data" button to reset the dashboard and clear all alerts
- Confirmation dialog prevents accidental data loss

## Data Validation

### Automatic Validation
- **Amounts**: Removes currency symbols, validates numeric values
- **Dates**: Handles Excel date formats and standard date strings
- **Dr/Cr**: Normalizes debit/credit indicators
- **Missing Data**: Handles empty cells gracefully

### Error Handling
- **File Format**: Validates Excel file types
- **Column Structure**: Ensures all required columns are present
- **Data Quality**: Provides warnings for invalid data
- **User Feedback**: Clear error messages and status updates

## Security Features
- **Client-side Processing**: No data transmitted to external servers
- **File Size Limits**: Prevents processing of excessively large files
- **Input Validation**: Comprehensive validation of all user inputs
- **Error Boundaries**: Graceful handling of processing errors

## Keyboard Shortcuts
- **Escape**: Close error dialogs
- **Ctrl+E**: Export data (when available)

## Alert System Details

### Alert Types
1. **Two-Day High-Value Debit/Credit Alert (Consolidated)**: Detects when a customer has both debit and credit transactions within a two-day period with identical amounts (≥300,000) and matching counter parties, consolidating all suspicious pairs for that customer/period into a single alert

### Alert Detection Criteria
- **Two-Day Window**: Transactions must occur within consecutive days (maximum 1-day gap)
- **Minimum Amount Threshold**: Both debit and credit transactions must be 300,000 or above
- **Identical Amounts**: Debit and credit transactions must have exactly matching amounts
- **Matching Counter Parties**: Both transactions must involve the same counter party
- **Flexible Order**: Alert triggers regardless of whether debit or credit occurs first

### Alert Consolidation Logic
- **Customer-Based Grouping**: Creates one alert per customer per two-day period, regardless of the number of matching transaction pairs
- **Comprehensive Coverage**: Captures all suspicious transaction pairs while reducing alert noise
- **Detailed Breakdown**: Alert details show all individual transaction pairs when investigated
- **Smart Summarization**: Alert descriptions include total amounts, pair counts, and date ranges

### Alert Management Features
- **Alert Statistics**: Real-time counts of new, reviewed, dismissed, and total alerts
- **Advanced Filtering**: Filter alerts by status, date range, and customer ID
- **Alert Details**: Comprehensive view showing all transaction pairs for the customer/date
- **Status Tracking**: Track alert lifecycle from new to reviewed/dismissed
- **Notes System**: Add investigative notes and comments to alerts
- **Bulk Actions**: Review or dismiss multiple alerts at once
- **Export Capabilities**: Export filtered alerts with full transaction details and pair breakdowns

## File Structure
```
Transaction Analysis Dashboard/
├── index.html          # Main application structure with alert management UI
├── styles.css          # Professional styling including alert system styles
├── script.js           # Core functionality with alert management system
├── create-test-excel.html  # Test data creation utility
└── README.md          # Documentation
```

## Sample Data Format

Your Excel file should have the following structure:

| Transaction ID | Trans Ref No | Customer Id | Customer Name | Account No | ... |
|---------------|--------------|-------------|---------------|------------|-----|
| TXN001        | REF001       | CUST001     | John Doe      | ACC001     | ... |
| TXN002        | REF002       | CUST002     | Jane Smith    | ACC002     | ... |

## Troubleshooting

### Common Issues
1. **"Missing required columns" error**: Ensure Excel file has all 21 columns in exact order
2. **"Invalid file format" error**: Use only .xlsx or .xls files
3. **"File too large" error**: Reduce file size to under 50MB
4. **Loading issues**: Ensure stable internet connection for CDN resources

### Performance Tips
- For large files (>10,000 rows), processing may take a few seconds
- Use modern browsers for optimal performance
- Close other browser tabs if experiencing memory issues

## Support
This application runs entirely in your browser and does not collect or transmit any data. All processing is performed locally on your device for maximum security and privacy.

## Version
Version 2.0 - Professional Transaction Analysis Dashboard with Alert Management System

## Recent Updates
- **Alert Management System**: Comprehensive alert detection and management capabilities
- **High-Value Alert Threshold**: Enhanced detection with minimum amount threshold of 300,000 for focused monitoring
- **Two-Day Alert Rule**: Enhanced detection algorithm for transactions within two-day periods
- **Counter Party Matching**: Advanced filtering requiring matching counter parties for alert generation
- **Consolidated Alert Approach**: Smart grouping of alerts by customer and two-day period to reduce noise
- **Enhanced UI**: Tabbed navigation and professional alert dashboard with date range display
- **Export Enhancements**: Export both transaction data and alerts with detailed breakdowns
- **Bulk Operations**: Manage multiple alerts simultaneously
- **Investigation Tools**: Add notes and track alert status changes
- **Multi-Pair Support**: Handle multiple suspicious transaction pairs per customer per two-day period
