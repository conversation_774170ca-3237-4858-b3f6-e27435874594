<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transaction Analysis Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <!-- SheetJS CDN for Excel parsing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-chart-line"></i> Transaction Analysis Dashboard</h1>
                <p class="subtitle">Professional Banking Transaction Analysis Tool</p>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Upload Section -->
            <section class="upload-section">
                <div class="upload-card">
                    <h2><i class="fas fa-upload"></i> Upload Transaction Data</h2>
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-content">
                            <i class="fas fa-file-excel upload-icon"></i>
                            <h3>Drag & Drop Excel File Here</h3>
                            <p>or <span class="browse-link" id="browseLink">browse to select file</span></p>
                            <p class="file-info">Supports .xlsx and .xls files</p>
                        </div>
                        <input type="file" id="fileInput" accept=".xlsx,.xls" hidden>
                    </div>
                    <div class="upload-status" id="uploadStatus"></div>
                </div>
            </section>

            <!-- Statistics Section -->
            <section class="stats-section" id="statsSection" style="display: none;">
                <h2><i class="fas fa-chart-bar"></i> Transaction Summary</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-list"></i></div>
                        <div class="stat-content">
                            <h3 id="totalTransactions">0</h3>
                            <p>Total Transactions</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-arrow-up text-success"></i></div>
                        <div class="stat-content">
                            <h3 id="creditAmount">$0</h3>
                            <p>Total Credits</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-arrow-down text-danger"></i></div>
                        <div class="stat-content">
                            <h3 id="debitAmount">$0</h3>
                            <p>Total Debits</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-balance-scale"></i></div>
                        <div class="stat-content">
                            <h3 id="netAmount">$0</h3>
                            <p>Net Amount</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Data Preview Section -->
            <section class="data-section" id="dataSection" style="display: none;">
                <div class="data-header">
                    <h2><i class="fas fa-table"></i> Transaction Data Preview</h2>
                    <div class="data-controls">
                        <button class="btn btn-secondary" id="exportBtn">
                            <i class="fas fa-download"></i> Export Data
                        </button>
                        <button class="btn btn-outline" id="clearBtn">
                            <i class="fas fa-trash"></i> Clear Data
                        </button>
                    </div>
                </div>
                <div class="table-container">
                    <table class="data-table" id="dataTable">
                        <thead>
                            <tr>
                                <th>Transaction ID</th>
                                <th>Trans Ref No</th>
                                <th>Customer ID</th>
                                <th>Customer Name</th>
                                <th>Account No</th>
                                <th>Account Open Date</th>
                                <th>Product Type</th>
                                <th>Branch</th>
                                <th>Date</th>
                                <th>Tran Amount</th>
                                <th>Tran Currency</th>
                                <th>Dr or Cr</th>
                                <th>Counter Party Name</th>
                                <th>Counter Customer ID</th>
                                <th>Counter Account No.</th>
                                <th>Remarks</th>
                                <th>Particulars</th>
                                <th>Transaction Location ID</th>
                                <th>Approved User ID</th>
                                <th>Entry User ID</th>
                                <th>Posted User ID</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody">
                        </tbody>
                    </table>
                </div>
                <div class="pagination" id="pagination">
                    <button class="btn btn-outline" id="prevBtn" disabled>
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    <span class="page-info" id="pageInfo">Page 1 of 1</span>
                    <button class="btn btn-outline" id="nextBtn" disabled>
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </section>
        </main>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-content">
                <div class="spinner"></div>
                <p>Processing transaction data...</p>
            </div>
        </div>

        <!-- Error Modal -->
        <div class="modal" id="errorModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-exclamation-triangle"></i> Error</h3>
                    <button class="close-btn" id="closeErrorModal">&times;</button>
                </div>
                <div class="modal-body">
                    <p id="errorMessage"></p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" id="errorOkBtn">OK</button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
