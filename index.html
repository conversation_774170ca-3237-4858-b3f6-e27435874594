<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Transaction Analysis Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <!-- SheetJS CDN for Excel parsing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-chart-line"></i> Transaction Analysis Dashboard</h1>
                <p class="subtitle">Professional Banking Transaction Analysis Tool</p>
            </div>
        </header>

        <!-- Navigation Tabs -->
        <nav class="nav-tabs">
            <div class="nav-container">
                <button class="nav-tab active" id="transactionsTab">
                    <i class="fas fa-table"></i> Transactions
                </button>
                <button class="nav-tab" id="alertsTab">
                    <i class="fas fa-exclamation-triangle"></i> Alerts
                    <span class="alert-badge" id="alertBadge" style="display: none;">0</span>
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Transactions View -->
            <div class="view-container" id="transactionsView">
                <!-- Upload Section -->
                <section class="upload-section">
                <div class="upload-card">
                    <h2><i class="fas fa-upload"></i> Upload Transaction Data</h2>
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-content">
                            <i class="fas fa-file-excel upload-icon"></i>
                            <h3>Drag & Drop Excel File Here</h3>
                            <p>or <span class="browse-link" id="browseLink">browse to select file</span></p>
                            <p class="file-info">Supports .xlsx and .xls files</p>
                        </div>
                        <input type="file" id="fileInput" accept=".xlsx,.xls" hidden>
                    </div>
                    <div class="upload-status" id="uploadStatus"></div>
                </div>
            </section>

            <!-- Statistics Section -->
            <section class="stats-section" id="statsSection" style="display: none;">
                <h2><i class="fas fa-chart-bar"></i> Transaction Summary</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-list"></i></div>
                        <div class="stat-content">
                            <h3 id="totalTransactions">0</h3>
                            <p>Total Transactions</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-arrow-up text-success"></i></div>
                        <div class="stat-content">
                            <h3 id="creditAmount">$0</h3>
                            <p>Total Credits</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-arrow-down text-danger"></i></div>
                        <div class="stat-content">
                            <h3 id="debitAmount">$0</h3>
                            <p>Total Debits</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-balance-scale"></i></div>
                        <div class="stat-content">
                            <h3 id="netAmount">$0</h3>
                            <p>Net Amount</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Data Preview Section -->
            <section class="data-section" id="dataSection" style="display: none;">
                <div class="data-header">
                    <h2><i class="fas fa-table"></i> Transaction Data Preview</h2>
                    <div class="data-controls">
                        <button class="btn btn-secondary" id="exportBtn">
                            <i class="fas fa-download"></i> Export Data
                        </button>
                        <button class="btn btn-outline" id="clearBtn">
                            <i class="fas fa-trash"></i> Clear Data
                        </button>
                    </div>
                </div>
                <div class="table-container">
                    <table class="data-table" id="dataTable">
                        <thead>
                            <tr>
                                <th>Transaction ID</th>
                                <th>Trans Ref No</th>
                                <th>Customer ID</th>
                                <th>Customer Name</th>
                                <th>Account No</th>
                                <th>Account Open Date</th>
                                <th>Product Type</th>
                                <th>Branch</th>
                                <th>Date</th>
                                <th>Tran Amount</th>
                                <th>Tran Currency</th>
                                <th>Dr or Cr</th>
                                <th>Counter Party Name</th>
                                <th>Counter Customer ID</th>
                                <th>Counter Account No.</th>
                                <th>Remarks</th>
                                <th>Particulars</th>
                                <th>Transaction Location ID</th>
                                <th>Approved User ID</th>
                                <th>Entry User ID</th>
                                <th>Posted User ID</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody">
                        </tbody>
                    </table>
                </div>
                <div class="pagination" id="pagination">
                    <button class="btn btn-outline" id="prevBtn" disabled>
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    <span class="page-info" id="pageInfo">Page 1 of 1</span>
                    <button class="btn btn-outline" id="nextBtn" disabled>
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </section>
            </div>

            <!-- Alerts View -->
            <div class="view-container" id="alertsView" style="display: none;">
                <!-- Alert Summary Section -->
                <section class="alert-summary-section">
                    <h2><i class="fas fa-shield-alt"></i> Alert Management Dashboard</h2>
                    <div class="alert-stats-grid">
                        <div class="alert-stat-card">
                            <div class="alert-stat-icon new"><i class="fas fa-exclamation-circle"></i></div>
                            <div class="alert-stat-content">
                                <h3 id="newAlertsCount">0</h3>
                                <p>New Alerts</p>
                            </div>
                        </div>
                        <div class="alert-stat-card">
                            <div class="alert-stat-icon reviewed"><i class="fas fa-eye"></i></div>
                            <div class="alert-stat-content">
                                <h3 id="reviewedAlertsCount">0</h3>
                                <p>Reviewed</p>
                            </div>
                        </div>
                        <div class="alert-stat-card">
                            <div class="alert-stat-icon dismissed"><i class="fas fa-times-circle"></i></div>
                            <div class="alert-stat-content">
                                <h3 id="dismissedAlertsCount">0</h3>
                                <p>Dismissed</p>
                            </div>
                        </div>
                        <div class="alert-stat-card">
                            <div class="alert-stat-icon total"><i class="fas fa-list"></i></div>
                            <div class="alert-stat-content">
                                <h3 id="totalAlertsCount">0</h3>
                                <p>Total Alerts</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Alert Controls Section -->
                <section class="alert-controls-section">
                    <div class="alert-controls-header">
                        <h3><i class="fas fa-filter"></i> Alert Filters & Actions</h3>
                        <div class="alert-actions">
                            <button class="btn btn-secondary" id="exportAlertsBtn">
                                <i class="fas fa-download"></i> Export Alerts
                            </button>
                            <button class="btn btn-outline" id="clearAlertsBtn">
                                <i class="fas fa-trash"></i> Clear All Alerts
                            </button>
                        </div>
                    </div>
                    <div class="alert-filters">
                        <div class="filter-group">
                            <label for="statusFilter">Status:</label>
                            <select id="statusFilter" class="filter-select">
                                <option value="all">All Statuses</option>
                                <option value="new">New</option>
                                <option value="reviewed">Reviewed</option>
                                <option value="dismissed">Dismissed</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="dateFromFilter">Date From:</label>
                            <input type="date" id="dateFromFilter" class="filter-input">
                        </div>
                        <div class="filter-group">
                            <label for="dateToFilter">Date To:</label>
                            <input type="date" id="dateToFilter" class="filter-input">
                        </div>
                        <div class="filter-group">
                            <label for="customerFilter">Customer ID:</label>
                            <input type="text" id="customerFilter" class="filter-input" placeholder="Enter Customer ID">
                        </div>
                        <button class="btn btn-primary" id="applyFiltersBtn">
                            <i class="fas fa-search"></i> Apply Filters
                        </button>
                        <button class="btn btn-outline" id="clearFiltersBtn">
                            <i class="fas fa-eraser"></i> Clear
                        </button>
                    </div>
                </section>

                <!-- Alerts List Section -->
                <section class="alerts-list-section">
                    <div class="alerts-header">
                        <h3><i class="fas fa-list-ul"></i> Alert Details</h3>
                        <div class="bulk-actions" id="bulkActions" style="display: none;">
                            <button class="btn btn-sm btn-secondary" id="bulkReviewBtn">
                                <i class="fas fa-eye"></i> Mark as Reviewed
                            </button>
                            <button class="btn btn-sm btn-outline" id="bulkDismissBtn">
                                <i class="fas fa-times"></i> Dismiss
                            </button>
                        </div>
                    </div>
                    <div class="alerts-container" id="alertsContainer">
                        <div class="no-alerts-message" id="noAlertsMessage">
                            <i class="fas fa-shield-alt"></i>
                            <h4>No Alerts Found</h4>
                            <p>Upload transaction data to generate alerts, or adjust your filters.</p>
                        </div>
                    </div>
                    <div class="alert-pagination" id="alertPagination" style="display: none;">
                        <button class="btn btn-outline" id="alertPrevBtn" disabled>
                            <i class="fas fa-chevron-left"></i> Previous
                        </button>
                        <span class="page-info" id="alertPageInfo">Page 1 of 1</span>
                        <button class="btn btn-outline" id="alertNextBtn" disabled>
                            Next <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </section>
            </div>
        </main>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-content">
                <div class="spinner"></div>
                <p>Processing transaction data...</p>
            </div>
        </div>

        <!-- Error Modal -->
        <div class="modal" id="errorModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-exclamation-triangle"></i> Error</h3>
                    <button class="close-btn" id="closeErrorModal">&times;</button>
                </div>
                <div class="modal-body">
                    <p id="errorMessage"></p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" id="errorOkBtn">OK</button>
                </div>
            </div>
        </div>

        <!-- Alert Detail Modal -->
        <div class="modal" id="alertDetailModal">
            <div class="modal-content alert-modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-exclamation-triangle"></i> Alert Details</h3>
                    <button class="close-btn" id="closeAlertModal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="alert-detail-content" id="alertDetailContent">
                        <!-- Alert details will be populated here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="markReviewedBtn">
                        <i class="fas fa-eye"></i> Mark as Reviewed
                    </button>
                    <button class="btn btn-outline" id="dismissAlertBtn">
                        <i class="fas fa-times"></i> Dismiss
                    </button>
                    <button class="btn btn-primary" id="closeAlertDetailBtn">Close</button>
                </div>
            </div>
        </div>

        <!-- Alert Notes Modal -->
        <div class="modal" id="alertNotesModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-sticky-note"></i> Add Note</h3>
                    <button class="close-btn" id="closeNotesModal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="alertNoteText">Note:</label>
                        <textarea id="alertNoteText" class="form-textarea" rows="4" placeholder="Enter your note here..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" id="cancelNoteBtn">Cancel</button>
                    <button class="btn btn-primary" id="saveNoteBtn">Save Note</button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
