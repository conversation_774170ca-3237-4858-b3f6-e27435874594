<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Data Generator - Transaction Analysis Dashboard</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f7fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            margin-top: 20px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 600px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Sample Transaction Data Generator</h1>
        
        <div class="info">
            <strong>Purpose:</strong> Generate sample Excel files to test the Transaction Analysis Dashboard.
            The generated file will contain all 21 required columns with realistic sample data.
        </div>

        <form id="generatorForm">
            <div class="grid">
                <div class="form-group">
                    <label for="numTransactions">Number of Transactions:</label>
                    <input type="number" id="numTransactions" min="10" max="10000" value="100">
                </div>
                
                <div class="form-group">
                    <label for="dateRange">Date Range (days back):</label>
                    <input type="number" id="dateRange" min="1" max="365" value="30">
                </div>
                
                <div class="form-group">
                    <label for="minAmount">Minimum Amount:</label>
                    <input type="number" id="minAmount" min="1" value="10" step="0.01">
                </div>
                
                <div class="form-group">
                    <label for="maxAmount">Maximum Amount:</label>
                    <input type="number" id="maxAmount" min="1" value="10000" step="0.01">
                </div>
            </div>
            
            <div class="form-group">
                <label for="currency">Currency:</label>
                <select id="currency">
                    <option value="USD">USD</option>
                    <option value="EUR">EUR</option>
                    <option value="GBP">GBP</option>
                    <option value="JPY">JPY</option>
                    <option value="CAD">CAD</option>
                </select>
            </div>

            <button type="submit" class="btn">Generate Sample Excel File</button>
        </form>
    </div>

    <script>
        // Required columns in exact order
        const requiredColumns = [
            'Transaction ID',
            'Trans Ref No',
            'Customer Id',
            'Customer Name',
            'Account No',
            'Account Open Date',
            'Product Type',
            'Branch',
            'Date',
            'Tran Amount',
            'Tran Currency',
            'Dr or Cr',
            'Counter Party Name',
            'Counter Customer ID',
            'Counter Account No.',
            'Remarks',
            'Particulars',
            'Transaction Location Id',
            'Approved User Id',
            'Entry User Id',
            'Posted User Id'
        ];

        // Sample data arrays
        const customerNames = [
            'John Smith', 'Jane Doe', 'Michael Johnson', 'Sarah Wilson', 'David Brown',
            'Emily Davis', 'Robert Miller', 'Lisa Anderson', 'William Taylor', 'Jennifer Thomas',
            'Christopher Jackson', 'Amanda White', 'Matthew Harris', 'Ashley Martin', 'Joshua Thompson'
        ];

        const productTypes = [
            'Savings Account', 'Checking Account', 'Credit Card', 'Personal Loan', 'Mortgage',
            'Business Account', 'Investment Account', 'Certificate of Deposit'
        ];

        const branches = [
            'Main Branch', 'Downtown Branch', 'Westside Branch', 'Eastside Branch', 'North Branch',
            'South Branch', 'Airport Branch', 'Mall Branch', 'University Branch', 'Corporate Branch'
        ];

        const counterParties = [
            'ABC Corporation', 'XYZ Ltd', 'Global Services Inc', 'Tech Solutions LLC', 'Retail Store Chain',
            'Manufacturing Co', 'Service Provider', 'Online Merchant', 'Utility Company', 'Government Agency'
        ];

        const transactionTypes = [
            'Wire Transfer', 'ACH Transfer', 'Check Deposit', 'ATM Withdrawal', 'Online Transfer',
            'Bill Payment', 'Direct Deposit', 'Card Payment', 'Cash Deposit', 'Interest Payment'
        ];

        document.getElementById('generatorForm').addEventListener('submit', function(e) {
            e.preventDefault();
            generateSampleData();
        });

        function generateSampleData() {
            const numTransactions = parseInt(document.getElementById('numTransactions').value);
            const dateRange = parseInt(document.getElementById('dateRange').value);
            const minAmount = parseFloat(document.getElementById('minAmount').value);
            const maxAmount = parseFloat(document.getElementById('maxAmount').value);
            const currency = document.getElementById('currency').value;

            const data = [requiredColumns]; // Header row

            for (let i = 1; i <= numTransactions; i++) {
                const row = generateTransactionRow(i, dateRange, minAmount, maxAmount, currency);
                data.push(row);
            }

            // Create Excel file
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet(data);
            
            // Set column widths for better readability
            const colWidths = requiredColumns.map(() => ({ wch: 15 }));
            ws['!cols'] = colWidths;

            XLSX.utils.book_append_sheet(wb, ws, 'Transaction Data');

            // Generate filename with timestamp
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
            const filename = `sample_transactions_${timestamp}.xlsx`;

            // Save file
            XLSX.writeFile(wb, filename);
        }

        function generateTransactionRow(index, dateRange, minAmount, maxAmount, currency) {
            const customerId = `CUST${String(index).padStart(6, '0')}`;
            const customerName = getRandomItem(customerNames);
            const accountNo = `ACC${String(Math.floor(Math.random() * 1000000)).padStart(8, '0')}`;
            const productType = getRandomItem(productTypes);
            const branch = getRandomItem(branches);
            const drCr = Math.random() > 0.5 ? 'Dr' : 'Cr';
            const amount = (Math.random() * (maxAmount - minAmount) + minAmount).toFixed(2);
            
            // Generate random date within range
            const today = new Date();
            const pastDate = new Date(today.getTime() - (Math.random() * dateRange * 24 * 60 * 60 * 1000));
            const transactionDate = pastDate.toISOString().split('T')[0];
            
            // Generate account open date (before transaction date)
            const accountOpenDate = new Date(pastDate.getTime() - (Math.random() * 365 * 24 * 60 * 60 * 1000));
            const openDate = accountOpenDate.toISOString().split('T')[0];

            return [
                `TXN${String(index).padStart(8, '0')}`, // Transaction ID
                `REF${String(index).padStart(8, '0')}`, // Trans Ref No
                customerId, // Customer Id
                customerName, // Customer Name
                accountNo, // Account No
                openDate, // Account Open Date
                productType, // Product Type
                branch, // Branch
                transactionDate, // Date
                amount, // Tran Amount
                currency, // Tran Currency
                drCr, // Dr or Cr
                getRandomItem(counterParties), // Counter Party Name
                `COUNTER${String(Math.floor(Math.random() * 100000)).padStart(6, '0')}`, // Counter Customer ID
                `CACC${String(Math.floor(Math.random() * 1000000)).padStart(8, '0')}`, // Counter Account No
                getRandomItem(transactionTypes), // Remarks
                `${getRandomItem(transactionTypes)} - ${customerName}`, // Particulars
                `LOC${String(Math.floor(Math.random() * 100)).padStart(3, '0')}`, // Transaction Location Id
                `USER${String(Math.floor(Math.random() * 1000)).padStart(4, '0')}`, // Approved User Id
                `USER${String(Math.floor(Math.random() * 1000)).padStart(4, '0')}`, // Entry User Id
                `USER${String(Math.floor(Math.random() * 1000)).padStart(4, '0')}` // Posted User Id
            ];
        }

        function getRandomItem(array) {
            return array[Math.floor(Math.random() * array.length)];
        }
    </script>
</body>
</html>
