// Transaction Analysis Dashboard JavaScript

// Global variables
let transactionData = [];
let currentPage = 1;
const rowsPerPage = 50;

// Required column names in exact order
const requiredColumns = [
    'Transaction ID',
    'Trans Ref No',
    'Customer Id',
    'Customer Name',
    'Account No',
    'Account Open Date',
    'Product Type',
    'Branch',
    'Date',
    'Tran Amount',
    'Tran Currency',
    'Dr or Cr',
    'Counter Party Name',
    'Counter Customer ID',
    'Counter Account No.',
    'Remarks',
    'Particulars',
    'Transaction Location Id',
    'Approved User Id',
    'Entry User Id',
    'Posted User Id'
];

// DOM elements
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const browseLink = document.getElementById('browseLink');
const uploadStatus = document.getElementById('uploadStatus');
const loadingOverlay = document.getElementById('loadingOverlay');
const statsSection = document.getElementById('statsSection');
const dataSection = document.getElementById('dataSection');
const dataTableBody = document.getElementById('dataTableBody');
const pagination = document.getElementById('pagination');
const prevBtn = document.getElementById('prevBtn');
const nextBtn = document.getElementById('nextBtn');
const pageInfo = document.getElementById('pageInfo');
const exportBtn = document.getElementById('exportBtn');
const clearBtn = document.getElementById('clearBtn');
const errorModal = document.getElementById('errorModal');
const errorMessage = document.getElementById('errorMessage');
const closeErrorModal = document.getElementById('closeErrorModal');
const errorOkBtn = document.getElementById('errorOkBtn');

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
});

function initializeEventListeners() {
    // File upload events
    uploadArea.addEventListener('click', () => fileInput.click());
    browseLink.addEventListener('click', (e) => {
        e.stopPropagation();
        fileInput.click();
    });
    
    fileInput.addEventListener('change', handleFileSelect);
    
    // Drag and drop events
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleFileDrop);
    
    // Button events
    exportBtn.addEventListener('click', exportData);
    clearBtn.addEventListener('click', clearData);
    prevBtn.addEventListener('click', () => changePage(-1));
    nextBtn.addEventListener('click', () => changePage(1));
    
    // Modal events
    closeErrorModal.addEventListener('click', closeModal);
    errorOkBtn.addEventListener('click', closeModal);
    errorModal.addEventListener('click', (e) => {
        if (e.target === errorModal) closeModal();
    });
}

function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleFileDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        processFile(files[0]);
    }
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        processFile(file);
    }
}

function processFile(file) {
    try {
        // Validate file size
        validateFileSize(file);

        // Validate file type
        const validTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];

        if (!validTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
            showError('Please select a valid Excel file (.xlsx or .xls)');
            return;
        }

        showLoading(true);
        showUploadStatus('Processing file...', 'info');
    } catch (error) {
        showError(error.message);
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            parseExcelFile(e.target.result, file.name);
        } catch (error) {
            showLoading(false);
            showError('Error reading file: ' + error.message);
        }
    };
    
    reader.onerror = function() {
        showLoading(false);
        showError('Error reading file. Please try again.');
    };
    
    reader.readAsArrayBuffer(file);
}

function parseExcelFile(data, fileName) {
    try {
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        
        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        if (jsonData.length === 0) {
            throw new Error('The Excel file appears to be empty.');
        }
        
        // Validate and process data
        validateAndProcessData(jsonData, fileName);
        
    } catch (error) {
        showLoading(false);
        showError('Error parsing Excel file: ' + error.message);
    }
}

function validateAndProcessData(rawData, fileName) {
    try {
        // Check if we have at least header row
        if (rawData.length < 1) {
            throw new Error('File must contain at least a header row.');
        }

        const headers = rawData[0];
        const dataRows = rawData.slice(1);

        // Debug: Log the actual headers found
        console.log('File headers found:', headers);
        console.log('Expected headers:', requiredColumns);

        // Validate required columns
        const missingColumns = validateColumns(headers);
        if (missingColumns.length > 0) {
            // Provide more detailed error message
            const detailedError = `Missing required columns: ${missingColumns.join(', ')}\n\n` +
                `Found ${headers.length} columns in file:\n${headers.map((h, i) => `${i + 1}. "${h}"`).join('\n')}\n\n` +
                `Expected ${requiredColumns.length} columns:\n${requiredColumns.map((h, i) => `${i + 1}. "${h}"`).join('\n')}`;
            throw new Error(detailedError);
        }

        // Process and validate data rows
        const processedData = processDataRows(dataRows, headers);

        if (processedData.length === 0) {
            throw new Error('No valid transaction data found in the file.');
        }

        // Store processed data
        transactionData = processedData;
        currentPage = 1;

        // Update UI
        showLoading(false);
        showUploadStatus(`Successfully loaded ${processedData.length} transactions from ${fileName}`, 'success');
        updateStatistics();
        displayData();
        showSections();

    } catch (error) {
        showLoading(false);
        showError(error.message);
    }
}

function validateColumns(headers) {
    const missingColumns = [];
    const normalizedHeaders = headers.map(h => h ? h.toString().toLowerCase().trim() : '');

    // Check if we have enough columns
    if (headers.length < requiredColumns.length) {
        throw new Error(`File must contain at least ${requiredColumns.length} columns. Found ${headers.length} columns.`);
    }

    // First, try exact positional matching (preferred)
    let exactMatch = true;
    for (let i = 0; i < requiredColumns.length; i++) {
        const requiredCol = requiredColumns[i].toLowerCase();
        const headerCol = normalizedHeaders[i];

        if (headerCol !== requiredCol) {
            exactMatch = false;
            break;
        }
    }

    if (exactMatch) {
        return []; // Perfect match, no missing columns
    }

    // If exact positional match fails, check if all required columns exist anywhere
    const foundColumns = new Set();

    for (const requiredCol of requiredColumns) {
        const normalizedRequired = requiredCol.toLowerCase();
        let found = false;

        for (let i = 0; i < normalizedHeaders.length; i++) {
            if (normalizedHeaders[i] === normalizedRequired) {
                foundColumns.add(requiredCol);
                found = true;
                break;
            }
        }

        if (!found) {
            missingColumns.push(`"${requiredCol}"`);
        }
    }

    // If we found all columns but not in the right order, provide helpful message
    if (missingColumns.length === 0 && !exactMatch) {
        console.warn('All required columns found but not in the expected order. Processing will continue but column order is recommended for best results.');

        // Reorder the data to match expected column order
        return []; // Allow processing to continue
    }

    return missingColumns;
}

function processDataRows(dataRows, headers) {
    const processedData = [];
    const errors = [];

    // Create column mapping for flexible column order support
    const columnMapping = createColumnMapping(headers);

    dataRows.forEach((row, index) => {
        try {
            // Skip empty rows
            if (!row || row.every(cell => !cell && cell !== 0)) {
                return;
            }

            const processedRow = processRow(row, index + 2, columnMapping); // +2 because we start from row 2 (after header)
            if (processedRow) {
                processedData.push(processedRow);
            }
        } catch (error) {
            errors.push(`Row ${index + 2}: ${error.message}`);
        }
    });

    // Show warnings for data validation errors (but don't stop processing)
    if (errors.length > 0 && errors.length < 10) {
        console.warn('Data validation warnings:', errors);
    }

    return processedData;
}

function createColumnMapping(headers) {
    const mapping = {};
    const normalizedHeaders = headers.map(h => h ? h.toString().toLowerCase().trim() : '');

    // Map each required column to its actual position in the file
    requiredColumns.forEach(requiredCol => {
        const normalizedRequired = requiredCol.toLowerCase();
        const index = normalizedHeaders.findIndex(h => h === normalizedRequired);
        mapping[requiredCol] = index >= 0 ? index : -1;
    });

    return mapping;
}

function processRow(row, rowNumber, columnMapping) {
    const processedRow = {};

    // Map each column to the processed row using the column mapping
    requiredColumns.forEach((colName) => {
        const columnIndex = columnMapping[colName];
        let value = columnIndex >= 0 ? row[columnIndex] : '';

        // Handle empty values
        if (value === undefined || value === null || value === '') {
            value = '';
        } else {
            value = value.toString().trim();
        }

        // Validate and format specific columns
        switch (colName) {
            case 'Tran Amount':
                processedRow[colName] = validateAndFormatAmount(value, rowNumber);
                break;
            case 'Date':
            case 'Account Open Date':
                processedRow[colName] = validateAndFormatDate(value, rowNumber);
                break;
            case 'Dr or Cr':
                processedRow[colName] = validateDrCr(value, rowNumber);
                break;
            default:
                processedRow[colName] = value;
        }
    });

    return processedRow;
}

function validateAndFormatAmount(value, rowNumber) {
    if (!value) return 0;
    
    // Remove currency symbols and commas
    const cleanValue = value.toString().replace(/[$,\s]/g, '');
    const numValue = parseFloat(cleanValue);
    
    if (isNaN(numValue)) {
        console.warn(`Row ${rowNumber}: Invalid amount "${value}", using 0`);
        return 0;
    }
    
    return numValue;
}

function validateAndFormatDate(value, rowNumber) {
    if (!value) return '';
    
    // Try to parse the date
    let date;
    
    // Handle Excel date serial numbers
    if (typeof value === 'number') {
        date = XLSX.SSF.parse_date_code(value);
        if (date) {
            return `${date.y}-${String(date.m).padStart(2, '0')}-${String(date.d).padStart(2, '0')}`;
        }
    }
    
    // Try to parse as regular date string
    date = new Date(value);
    if (!isNaN(date.getTime())) {
        return date.toISOString().split('T')[0];
    }
    
    console.warn(`Row ${rowNumber}: Invalid date "${value}"`);
    return value.toString();
}

function validateDrCr(value, rowNumber) {
    if (!value) return '';

    const normalizedValue = value.toString().toLowerCase().trim();

    if (normalizedValue === 'dr' || normalizedValue === 'debit') {
        return 'Dr';
    } else if (normalizedValue === 'cr' || normalizedValue === 'credit') {
        return 'Cr';
    }

    console.warn(`Row ${rowNumber}: Invalid Dr/Cr value "${value}"`);
    return value.toString();
}

function updateStatistics() {
    const totalTransactions = transactionData.length;
    let totalCredits = 0;
    let totalDebits = 0;

    transactionData.forEach(row => {
        const amount = parseFloat(row['Tran Amount']) || 0;
        const drCr = row['Dr or Cr'];

        if (drCr === 'Cr') {
            totalCredits += amount;
        } else if (drCr === 'Dr') {
            totalDebits += amount;
        }
    });

    const netAmount = totalCredits - totalDebits;

    // Update DOM elements
    document.getElementById('totalTransactions').textContent = totalTransactions.toLocaleString();
    document.getElementById('creditAmount').textContent = formatCurrency(totalCredits);
    document.getElementById('debitAmount').textContent = formatCurrency(totalDebits);
    document.getElementById('netAmount').textContent = formatCurrency(netAmount);

    // Update net amount color based on positive/negative
    const netElement = document.getElementById('netAmount');
    netElement.className = netAmount >= 0 ? 'text-success' : 'text-danger';
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2
    }).format(amount);
}

function displayData() {
    const startIndex = (currentPage - 1) * rowsPerPage;
    const endIndex = startIndex + rowsPerPage;
    const pageData = transactionData.slice(startIndex, endIndex);

    // Clear existing table data
    dataTableBody.innerHTML = '';

    // Populate table with current page data
    pageData.forEach(row => {
        const tr = document.createElement('tr');

        requiredColumns.forEach(colName => {
            const td = document.createElement('td');
            let value = row[colName];

            // Format specific columns for display
            if (colName === 'Tran Amount') {
                value = formatCurrency(parseFloat(value) || 0);
            } else if (colName === 'Dr or Cr') {
                td.className = value === 'Cr' ? 'text-success' : value === 'Dr' ? 'text-danger' : '';
            }

            td.textContent = value || '';
            tr.appendChild(td);
        });

        dataTableBody.appendChild(tr);
    });

    updatePagination();
}

function updatePagination() {
    const totalPages = Math.ceil(transactionData.length / rowsPerPage);

    // Update page info
    pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;

    // Update button states
    prevBtn.disabled = currentPage <= 1;
    nextBtn.disabled = currentPage >= totalPages;
}

function changePage(direction) {
    const totalPages = Math.ceil(transactionData.length / rowsPerPage);
    const newPage = currentPage + direction;

    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        displayData();
    }
}

function showSections() {
    statsSection.style.display = 'block';
    dataSection.style.display = 'block';
}

function hideSections() {
    statsSection.style.display = 'none';
    dataSection.style.display = 'none';
}

function exportData() {
    if (transactionData.length === 0) {
        showError('No data to export');
        return;
    }

    try {
        // Create new workbook
        const wb = XLSX.utils.book_new();

        // Convert data to worksheet format
        const wsData = [requiredColumns];
        transactionData.forEach(row => {
            const rowData = requiredColumns.map(col => row[col] || '');
            wsData.push(rowData);
        });

        const ws = XLSX.utils.aoa_to_sheet(wsData);

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Transaction Data');

        // Generate filename with timestamp
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `transaction_analysis_${timestamp}.xlsx`;

        // Save file
        XLSX.writeFile(wb, filename);

        showUploadStatus(`Data exported successfully as ${filename}`, 'success');

    } catch (error) {
        showError('Error exporting data: ' + error.message);
    }
}

function clearData() {
    if (confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
        transactionData = [];
        currentPage = 1;
        dataTableBody.innerHTML = '';
        hideSections();
        showUploadStatus('', '');
        fileInput.value = '';
    }
}

function showLoading(show) {
    loadingOverlay.style.display = show ? 'flex' : 'none';
}

function showUploadStatus(message, type) {
    uploadStatus.textContent = message;
    uploadStatus.className = `upload-status ${type}`;
    uploadStatus.style.display = message ? 'block' : 'none';
}

function showError(message) {
    errorMessage.textContent = message;
    errorModal.style.display = 'flex';
}

function closeModal() {
    errorModal.style.display = 'none';
}

// Utility functions for better user experience
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Escape key to close modal
    if (e.key === 'Escape') {
        closeModal();
    }

    // Ctrl+E to export (when data is available)
    if (e.ctrlKey && e.key === 'e' && transactionData.length > 0) {
        e.preventDefault();
        exportData();
    }
});

// Add file size validation
function validateFileSize(file) {
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
        throw new Error('File size too large. Please select a file smaller than 50MB.');
    }
}
