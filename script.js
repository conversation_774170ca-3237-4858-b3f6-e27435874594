// Transaction Analysis Dashboard JavaScript

// Global variables
let transactionData = [];
let currentPage = 1;
const rowsPerPage = 50;

// Alert management variables
let alertsData = [];
let currentAlertPage = 1;
const alertsPerPage = 10;
let filteredAlerts = [];
let selectedAlerts = new Set();
let currentAlertFilters = {
    status: 'all',
    dateFrom: '',
    dateTo: '',
    customer: ''
};

// Alert configuration constants
const MINIMUM_AMOUNT_THRESHOLD = 300000; // Minimum amount threshold for high-value alerts

// Required column names in exact order
const requiredColumns = [
    'Transaction ID',
    'Trans Ref No',
    'Customer Id',
    'Customer Name',
    'Account No',
    'Account Open Date',
    'Product Type',
    'Branch',
    'Date',
    'Tran Amount',
    'Tran Currency',
    'Dr or Cr',
    'Counter Party Name',
    'Counter Customer ID',
    'Counter Account No.',
    'Remarks',
    'Particulars',
    'Transaction Location Id',
    'Approved User Id',
    'Entry User Id',
    'Posted User Id'
];

// DOM elements
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const browseLink = document.getElementById('browseLink');
const uploadStatus = document.getElementById('uploadStatus');
const loadingOverlay = document.getElementById('loadingOverlay');
const statsSection = document.getElementById('statsSection');
const dataSection = document.getElementById('dataSection');
const dataTableBody = document.getElementById('dataTableBody');
const pagination = document.getElementById('pagination');
const prevBtn = document.getElementById('prevBtn');
const nextBtn = document.getElementById('nextBtn');
const pageInfo = document.getElementById('pageInfo');
const exportBtn = document.getElementById('exportBtn');
const clearBtn = document.getElementById('clearBtn');
const errorModal = document.getElementById('errorModal');
const errorMessage = document.getElementById('errorMessage');
const closeErrorModal = document.getElementById('closeErrorModal');
const errorOkBtn = document.getElementById('errorOkBtn');

// Navigation and Alert DOM elements
const transactionsTab = document.getElementById('transactionsTab');
const alertsTab = document.getElementById('alertsTab');
const transactionsView = document.getElementById('transactionsView');
const alertsView = document.getElementById('alertsView');
const alertBadge = document.getElementById('alertBadge');

// Alert statistics elements
const newAlertsCount = document.getElementById('newAlertsCount');
const reviewedAlertsCount = document.getElementById('reviewedAlertsCount');
const dismissedAlertsCount = document.getElementById('dismissedAlertsCount');
const totalAlertsCount = document.getElementById('totalAlertsCount');

// Alert controls elements
const statusFilter = document.getElementById('statusFilter');
const dateFromFilter = document.getElementById('dateFromFilter');
const dateToFilter = document.getElementById('dateToFilter');
const customerFilter = document.getElementById('customerFilter');
const applyFiltersBtn = document.getElementById('applyFiltersBtn');
const clearFiltersBtn = document.getElementById('clearFiltersBtn');
const exportAlertsBtn = document.getElementById('exportAlertsBtn');
const clearAlertsBtn = document.getElementById('clearAlertsBtn');

// Alert list elements
const alertsContainer = document.getElementById('alertsContainer');
const noAlertsMessage = document.getElementById('noAlertsMessage');
const bulkActions = document.getElementById('bulkActions');
const bulkReviewBtn = document.getElementById('bulkReviewBtn');
const bulkDismissBtn = document.getElementById('bulkDismissBtn');
const alertPagination = document.getElementById('alertPagination');
const alertPrevBtn = document.getElementById('alertPrevBtn');
const alertNextBtn = document.getElementById('alertNextBtn');
const alertPageInfo = document.getElementById('alertPageInfo');

// Alert modal elements
const alertDetailModal = document.getElementById('alertDetailModal');
const alertDetailContent = document.getElementById('alertDetailContent');
const closeAlertModal = document.getElementById('closeAlertModal');
const markReviewedBtn = document.getElementById('markReviewedBtn');
const dismissAlertBtn = document.getElementById('dismissAlertBtn');
const closeAlertDetailBtn = document.getElementById('closeAlertDetailBtn');

// Alert notes modal elements
const alertNotesModal = document.getElementById('alertNotesModal');
const alertNoteText = document.getElementById('alertNoteText');
const closeNotesModal = document.getElementById('closeNotesModal');
const cancelNoteBtn = document.getElementById('cancelNoteBtn');
const saveNoteBtn = document.getElementById('saveNoteBtn');

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
});

function initializeEventListeners() {
    // File upload events
    uploadArea.addEventListener('click', () => fileInput.click());
    browseLink.addEventListener('click', (e) => {
        e.stopPropagation();
        fileInput.click();
    });

    fileInput.addEventListener('change', handleFileSelect);

    // Drag and drop events
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleFileDrop);

    // Button events
    exportBtn.addEventListener('click', exportData);
    clearBtn.addEventListener('click', clearData);
    prevBtn.addEventListener('click', () => changePage(-1));
    nextBtn.addEventListener('click', () => changePage(1));

    // Modal events
    closeErrorModal.addEventListener('click', closeModal);
    errorOkBtn.addEventListener('click', closeModal);
    errorModal.addEventListener('click', (e) => {
        if (e.target === errorModal) closeModal();
    });

    // Navigation events
    transactionsTab.addEventListener('click', () => switchView('transactions'));
    alertsTab.addEventListener('click', () => switchView('alerts'));

    // Alert filter events
    applyFiltersBtn.addEventListener('click', applyAlertFilters);
    clearFiltersBtn.addEventListener('click', clearAlertFilters);
    statusFilter.addEventListener('change', applyAlertFilters);

    // Alert action events
    exportAlertsBtn.addEventListener('click', exportAlerts);
    clearAlertsBtn.addEventListener('click', clearAllAlerts);
    bulkReviewBtn.addEventListener('click', () => bulkUpdateAlerts('reviewed'));
    bulkDismissBtn.addEventListener('click', () => bulkUpdateAlerts('dismissed'));

    // Alert pagination events
    alertPrevBtn.addEventListener('click', () => changeAlertPage(-1));
    alertNextBtn.addEventListener('click', () => changeAlertPage(1));

    // Alert modal events
    closeAlertModal.addEventListener('click', closeAlertDetailModal);
    closeAlertDetailBtn.addEventListener('click', closeAlertDetailModal);
    markReviewedBtn.addEventListener('click', () => updateCurrentAlertStatus('reviewed'));
    dismissAlertBtn.addEventListener('click', () => updateCurrentAlertStatus('dismissed'));
    alertDetailModal.addEventListener('click', (e) => {
        if (e.target === alertDetailModal) closeAlertDetailModal();
    });

    // Alert notes modal events
    closeNotesModal.addEventListener('click', closeAlertNotesModal);
    cancelNoteBtn.addEventListener('click', closeAlertNotesModal);
    saveNoteBtn.addEventListener('click', saveAlertNote);
    alertNotesModal.addEventListener('click', (e) => {
        if (e.target === alertNotesModal) closeAlertNotesModal();
    });
}

function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleFileDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        processFile(files[0]);
    }
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        processFile(file);
    }
}

function processFile(file) {
    try {
        // Validate file size
        validateFileSize(file);

        // Validate file type
        const validTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];

        if (!validTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
            showError('Please select a valid Excel file (.xlsx or .xls)');
            return;
        }

        showLoading(true);
        showUploadStatus('Processing file...', 'info');
    } catch (error) {
        showError(error.message);
        return;
    }
    
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            parseExcelFile(e.target.result, file.name);
        } catch (error) {
            showLoading(false);
            showError('Error reading file: ' + error.message);
        }
    };
    
    reader.onerror = function() {
        showLoading(false);
        showError('Error reading file. Please try again.');
    };
    
    reader.readAsArrayBuffer(file);
}

function parseExcelFile(data, fileName) {
    try {
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        
        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        if (jsonData.length === 0) {
            throw new Error('The Excel file appears to be empty.');
        }
        
        // Validate and process data
        validateAndProcessData(jsonData, fileName);
        
    } catch (error) {
        showLoading(false);
        showError('Error parsing Excel file: ' + error.message);
    }
}

function validateAndProcessData(rawData, fileName) {
    try {
        // Check if we have at least header row
        if (rawData.length < 1) {
            throw new Error('File must contain at least a header row.');
        }

        const headers = rawData[0];
        const dataRows = rawData.slice(1);

        // Debug: Log the actual headers found
        console.log('File headers found:', headers);
        console.log('Expected headers:', requiredColumns);

        // Validate required columns
        const missingColumns = validateColumns(headers);
        if (missingColumns.length > 0) {
            // Provide more detailed error message
            const detailedError = `Missing required columns: ${missingColumns.join(', ')}\n\n` +
                `Found ${headers.length} columns in file:\n${headers.map((h, i) => `${i + 1}. "${h}"`).join('\n')}\n\n` +
                `Expected ${requiredColumns.length} columns:\n${requiredColumns.map((h, i) => `${i + 1}. "${h}"`).join('\n')}`;
            throw new Error(detailedError);
        }

        // Process and validate data rows
        const processedData = processDataRows(dataRows, headers);

        if (processedData.length === 0) {
            throw new Error('No valid transaction data found in the file.');
        }

        // Store processed data
        transactionData = processedData;
        currentPage = 1;

        // Update UI
        showLoading(false);
        showUploadStatus(`Successfully loaded ${processedData.length} transactions from ${fileName}`, 'success');
        updateStatistics();
        displayData();
        showSections();

        // Generate alerts
        generateAlerts();
        updateAlertBadge();

    } catch (error) {
        showLoading(false);
        showError(error.message);
    }
}

function validateColumns(headers) {
    const missingColumns = [];
    const normalizedHeaders = headers.map(h => h ? h.toString().toLowerCase().trim() : '');

    // Check if we have enough columns
    if (headers.length < requiredColumns.length) {
        throw new Error(`File must contain at least ${requiredColumns.length} columns. Found ${headers.length} columns.`);
    }

    // First, try exact positional matching (preferred)
    let exactMatch = true;
    for (let i = 0; i < requiredColumns.length; i++) {
        const requiredCol = requiredColumns[i].toLowerCase();
        const headerCol = normalizedHeaders[i];

        if (headerCol !== requiredCol) {
            exactMatch = false;
            break;
        }
    }

    if (exactMatch) {
        return []; // Perfect match, no missing columns
    }

    // If exact positional match fails, check if all required columns exist anywhere
    const foundColumns = new Set();

    for (const requiredCol of requiredColumns) {
        const normalizedRequired = requiredCol.toLowerCase();
        let found = false;

        for (let i = 0; i < normalizedHeaders.length; i++) {
            if (normalizedHeaders[i] === normalizedRequired) {
                foundColumns.add(requiredCol);
                found = true;
                break;
            }
        }

        if (!found) {
            missingColumns.push(`"${requiredCol}"`);
        }
    }

    // If we found all columns but not in the right order, provide helpful message
    if (missingColumns.length === 0 && !exactMatch) {
        console.warn('All required columns found but not in the expected order. Processing will continue but column order is recommended for best results.');

        // Reorder the data to match expected column order
        return []; // Allow processing to continue
    }

    return missingColumns;
}

function processDataRows(dataRows, headers) {
    const processedData = [];
    const errors = [];

    // Create column mapping for flexible column order support
    const columnMapping = createColumnMapping(headers);

    dataRows.forEach((row, index) => {
        try {
            // Skip empty rows
            if (!row || row.every(cell => !cell && cell !== 0)) {
                return;
            }

            const processedRow = processRow(row, index + 2, columnMapping); // +2 because we start from row 2 (after header)
            if (processedRow) {
                processedData.push(processedRow);
            }
        } catch (error) {
            errors.push(`Row ${index + 2}: ${error.message}`);
        }
    });

    // Show warnings for data validation errors (but don't stop processing)
    if (errors.length > 0 && errors.length < 10) {
        console.warn('Data validation warnings:', errors);
    }

    return processedData;
}

function createColumnMapping(headers) {
    const mapping = {};
    const normalizedHeaders = headers.map(h => h ? h.toString().toLowerCase().trim() : '');

    // Map each required column to its actual position in the file
    requiredColumns.forEach(requiredCol => {
        const normalizedRequired = requiredCol.toLowerCase();
        const index = normalizedHeaders.findIndex(h => h === normalizedRequired);
        mapping[requiredCol] = index >= 0 ? index : -1;
    });

    return mapping;
}

function processRow(row, rowNumber, columnMapping) {
    const processedRow = {};

    // Map each column to the processed row using the column mapping
    requiredColumns.forEach((colName) => {
        const columnIndex = columnMapping[colName];
        let value = columnIndex >= 0 ? row[columnIndex] : '';

        // Handle empty values
        if (value === undefined || value === null || value === '') {
            value = '';
        } else {
            value = value.toString().trim();
        }

        // Validate and format specific columns
        switch (colName) {
            case 'Tran Amount':
                processedRow[colName] = validateAndFormatAmount(value, rowNumber);
                break;
            case 'Date':
            case 'Account Open Date':
                processedRow[colName] = validateAndFormatDate(value, rowNumber);
                break;
            case 'Dr or Cr':
                processedRow[colName] = validateDrCr(value, rowNumber);
                break;
            default:
                processedRow[colName] = value;
        }
    });

    return processedRow;
}

function validateAndFormatAmount(value, rowNumber) {
    if (!value) return 0;
    
    // Remove currency symbols and commas
    const cleanValue = value.toString().replace(/[$,\s]/g, '');
    const numValue = parseFloat(cleanValue);
    
    if (isNaN(numValue)) {
        console.warn(`Row ${rowNumber}: Invalid amount "${value}", using 0`);
        return 0;
    }
    
    return numValue;
}

function validateAndFormatDate(value, rowNumber) {
    if (!value) return '';
    
    // Try to parse the date
    let date;
    
    // Handle Excel date serial numbers
    if (typeof value === 'number') {
        date = XLSX.SSF.parse_date_code(value);
        if (date) {
            return `${date.y}-${String(date.m).padStart(2, '0')}-${String(date.d).padStart(2, '0')}`;
        }
    }
    
    // Try to parse as regular date string
    date = new Date(value);
    if (!isNaN(date.getTime())) {
        return date.toISOString().split('T')[0];
    }
    
    console.warn(`Row ${rowNumber}: Invalid date "${value}"`);
    return value.toString();
}

function validateDrCr(value, rowNumber) {
    if (!value) return '';

    const normalizedValue = value.toString().toLowerCase().trim();

    if (normalizedValue === 'dr' || normalizedValue === 'debit') {
        return 'Dr';
    } else if (normalizedValue === 'cr' || normalizedValue === 'credit') {
        return 'Cr';
    }

    console.warn(`Row ${rowNumber}: Invalid Dr/Cr value "${value}"`);
    return value.toString();
}

function updateStatistics() {
    const totalTransactions = transactionData.length;
    let totalCredits = 0;
    let totalDebits = 0;

    transactionData.forEach(row => {
        const amount = parseFloat(row['Tran Amount']) || 0;
        const drCr = row['Dr or Cr'];

        if (drCr === 'Cr') {
            totalCredits += amount;
        } else if (drCr === 'Dr') {
            totalDebits += amount;
        }
    });

    const netAmount = totalCredits - totalDebits;

    // Update DOM elements
    document.getElementById('totalTransactions').textContent = totalTransactions.toLocaleString();
    document.getElementById('creditAmount').textContent = formatCurrency(totalCredits);
    document.getElementById('debitAmount').textContent = formatCurrency(totalDebits);
    document.getElementById('netAmount').textContent = formatCurrency(netAmount);

    // Update net amount color based on positive/negative
    const netElement = document.getElementById('netAmount');
    netElement.className = netAmount >= 0 ? 'text-success' : 'text-danger';
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2
    }).format(amount);
}

function displayData() {
    const startIndex = (currentPage - 1) * rowsPerPage;
    const endIndex = startIndex + rowsPerPage;
    const pageData = transactionData.slice(startIndex, endIndex);

    // Clear existing table data
    dataTableBody.innerHTML = '';

    // Populate table with current page data
    pageData.forEach(row => {
        const tr = document.createElement('tr');

        requiredColumns.forEach(colName => {
            const td = document.createElement('td');
            let value = row[colName];

            // Format specific columns for display
            if (colName === 'Tran Amount') {
                value = formatCurrency(parseFloat(value) || 0);
            } else if (colName === 'Dr or Cr') {
                td.className = value === 'Cr' ? 'text-success' : value === 'Dr' ? 'text-danger' : '';
            }

            td.textContent = value || '';
            tr.appendChild(td);
        });

        dataTableBody.appendChild(tr);
    });

    updatePagination();
}

function updatePagination() {
    const totalPages = Math.ceil(transactionData.length / rowsPerPage);

    // Update page info
    pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;

    // Update button states
    prevBtn.disabled = currentPage <= 1;
    nextBtn.disabled = currentPage >= totalPages;
}

function changePage(direction) {
    const totalPages = Math.ceil(transactionData.length / rowsPerPage);
    const newPage = currentPage + direction;

    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        displayData();
    }
}

function showSections() {
    statsSection.style.display = 'block';
    dataSection.style.display = 'block';
}

function hideSections() {
    statsSection.style.display = 'none';
    dataSection.style.display = 'none';
}

function exportData() {
    if (transactionData.length === 0) {
        showError('No data to export');
        return;
    }

    try {
        // Create new workbook
        const wb = XLSX.utils.book_new();

        // Convert data to worksheet format
        const wsData = [requiredColumns];
        transactionData.forEach(row => {
            const rowData = requiredColumns.map(col => row[col] || '');
            wsData.push(rowData);
        });

        const ws = XLSX.utils.aoa_to_sheet(wsData);

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Transaction Data');

        // Generate filename with timestamp
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `transaction_analysis_${timestamp}.xlsx`;

        // Save file
        XLSX.writeFile(wb, filename);

        showUploadStatus(`Data exported successfully as ${filename}`, 'success');

    } catch (error) {
        showError('Error exporting data: ' + error.message);
    }
}

function clearData() {
    if (confirm('Are you sure you want to clear all data? This action cannot be undone.')) {
        transactionData = [];
        currentPage = 1;
        dataTableBody.innerHTML = '';
        hideSections();
        showUploadStatus('', '');
        fileInput.value = '';
    }
}

function showLoading(show) {
    loadingOverlay.style.display = show ? 'flex' : 'none';
}

function showUploadStatus(message, type) {
    uploadStatus.textContent = message;
    uploadStatus.className = `upload-status ${type}`;
    uploadStatus.style.display = message ? 'block' : 'none';
}

function showError(message) {
    errorMessage.textContent = message;
    errorModal.style.display = 'flex';
}

function closeModal() {
    errorModal.style.display = 'none';
}

// Utility functions for better user experience
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Escape key to close modal
    if (e.key === 'Escape') {
        closeModal();
    }

    // Ctrl+E to export (when data is available)
    if (e.ctrlKey && e.key === 'e' && transactionData.length > 0) {
        e.preventDefault();
        exportData();
    }
});

// Add file size validation
function validateFileSize(file) {
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
        throw new Error('File size too large. Please select a file smaller than 50MB.');
    }
}

// ============================================================================
// ALERT MANAGEMENT SYSTEM
// ============================================================================

// Generate alerts from transaction data
function generateAlerts() {
    alertsData = [];

    if (transactionData.length === 0) {
        updateAlertStatistics();
        return;
    }

    // Group transactions by customer for two-day analysis
    const customerTransactions = groupTransactionsByCustomer();

    // Detect two-day debit/credit alerts
    detectTwoDayDebitCreditAlerts(customerTransactions);

    // Update alert statistics and display
    updateAlertStatistics();
    applyAlertFilters();

    console.log(`Generated ${alertsData.length} alerts`);
}

// Group transactions by customer ID for two-day analysis
function groupTransactionsByCustomer() {
    const groups = {};

    transactionData.forEach((transaction, index) => {
        const customerId = transaction['Customer Id'] || '';
        const date = transaction['Date'] || '';
        const amount = parseFloat(transaction['Tran Amount']) || 0;
        const drCr = transaction['Dr or Cr'] || '';
        const counterParty = transaction['Counter Party Name'] || '';

        if (!customerId || !date || amount === 0 || !drCr || !counterParty) {
            return; // Skip invalid transactions
        }

        if (!groups[customerId]) {
            groups[customerId] = {
                customerId,
                transactions: []
            };
        }

        groups[customerId].transactions.push({
            ...transaction,
            originalIndex: index,
            amount,
            drCr,
            counterParty,
            date: new Date(date) // Convert to Date object for easier comparison
        });
    });

    return groups;
}

// Detect two-day debit/credit alerts
function detectTwoDayDebitCreditAlerts(customerGroups) {
    Object.values(customerGroups).forEach(group => {
        const { customerId, transactions } = group;

        // Sort transactions by date for easier processing
        transactions.sort((a, b) => a.date - b.date);

        // Find all two-day periods with matching debit/credit pairs
        const twoDayPeriods = findTwoDayPeriods(transactions);

        // Process each two-day period
        twoDayPeriods.forEach(period => {
            const transactionPairs = findMatchingPairs(period.transactions);

            if (transactionPairs.length > 0) {
                const totalAmount = transactionPairs.reduce((sum, pair) => sum + pair.amount, 0);
                createConsolidatedTwoDayAlert(customerId, period, transactionPairs, totalAmount);
            }
        });
    });
}

// Find all unique two-day periods for a customer's transactions
function findTwoDayPeriods(transactions) {
    const periods = [];
    const processedPeriods = new Set();

    for (let i = 0; i < transactions.length; i++) {
        const currentDate = transactions[i].date;
        const periodKey = formatDateForPeriod(currentDate);

        if (processedPeriods.has(periodKey)) {
            continue;
        }

        // Find all transactions within 2 days of current date
        const periodTransactions = transactions.filter(transaction => {
            const daysDiff = Math.abs((transaction.date - currentDate) / (1000 * 60 * 60 * 24));
            return daysDiff <= 1; // Within 2 days (0-1 day difference)
        });

        if (periodTransactions.length > 1) {
            periods.push({
                startDate: new Date(Math.min(...periodTransactions.map(t => t.date))),
                endDate: new Date(Math.max(...periodTransactions.map(t => t.date))),
                transactions: periodTransactions
            });
            processedPeriods.add(periodKey);
        }
    }

    return periods;
}

// Find matching debit/credit pairs within a two-day period
function findMatchingPairs(transactions) {
    const pairs = [];

    // Group by amount and counter party
    const groups = {};
    transactions.forEach(transaction => {
        // Only consider transactions that meet the minimum amount threshold
        if (transaction.amount >= MINIMUM_AMOUNT_THRESHOLD) {
            const key = `${transaction.amount}_${transaction.counterParty}`;
            if (!groups[key]) {
                groups[key] = { debits: [], credits: [] };
            }

            if (transaction.drCr === 'Dr') {
                groups[key].debits.push(transaction);
            } else if (transaction.drCr === 'Cr') {
                groups[key].credits.push(transaction);
            }
        }
    });

    // Find matching pairs
    Object.entries(groups).forEach(([key, { debits, credits }]) => {
        if (debits.length > 0 && credits.length > 0) {
            // Create pairs for each debit/credit combination
            debits.forEach(debit => {
                credits.forEach(credit => {
                    // Double-check that both transactions meet the threshold
                    if (debit.amount >= MINIMUM_AMOUNT_THRESHOLD && credit.amount >= MINIMUM_AMOUNT_THRESHOLD) {
                        pairs.push({
                            debitTransaction: debit,
                            creditTransaction: credit,
                            amount: debit.amount,
                            counterParty: debit.counterParty
                        });
                    }
                });
            });
        }
    });

    return pairs;
}

// Helper function to format date for period grouping
function formatDateForPeriod(date) {
    return date.toISOString().split('T')[0];
}

// Create a consolidated two-day debit/credit alert
function createConsolidatedTwoDayAlert(customerId, period, transactionPairs, totalAmount) {
    const customerName = transactionPairs[0].debitTransaction['Customer Name'] || '';
    const pairCount = transactionPairs.length;
    const startDate = formatDateForDisplay(period.startDate);
    const endDate = formatDateForDisplay(period.endDate);
    const dateRange = startDate === endDate ? startDate : `${startDate} to ${endDate}`;

    const alert = {
        id: generateAlertId(),
        type: 'two_day_debit_credit_consolidated',
        title: 'Two-Day High-Value Debit/Credit Alert',
        description: `Customer has ${pairCount} matching high-value debit/credit transaction pair${pairCount > 1 ? 's' : ''} (≥300,000 each) with same counter party within 2 days (${dateRange}) (Total: ${formatCurrency(totalAmount)})`,
        severity: pairCount > 2 ? 'high' : 'medium',
        status: 'new',
        customerId: customerId,
        customerName: customerName,
        dateRange: dateRange,
        startDate: startDate,
        endDate: endDate,
        totalAmount: totalAmount,
        pairCount: pairCount,
        transactionPairs: transactionPairs,
        timestamp: new Date().toISOString(),
        notes: []
    };

    alertsData.push(alert);
}

// Helper function to format date for display
function formatDateForDisplay(date) {
    return date.toISOString().split('T')[0];
}

// Generate unique alert ID
function generateAlertId() {
    return 'alert_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// Navigation functions
function switchView(view) {
    if (view === 'transactions') {
        transactionsTab.classList.add('active');
        alertsTab.classList.remove('active');
        transactionsView.style.display = 'block';
        alertsView.style.display = 'none';
    } else if (view === 'alerts') {
        transactionsTab.classList.remove('active');
        alertsTab.classList.add('active');
        transactionsView.style.display = 'none';
        alertsView.style.display = 'block';

        // Refresh alerts display when switching to alerts view
        applyAlertFilters();
    }
}

// Update alert badge
function updateAlertBadge() {
    const newAlerts = alertsData.filter(alert => alert.status === 'new').length;

    if (newAlerts > 0) {
        alertBadge.textContent = newAlerts;
        alertBadge.style.display = 'inline-block';
        alertBadge.classList.add('pulse');
    } else {
        alertBadge.style.display = 'none';
        alertBadge.classList.remove('pulse');
    }
}

// Update alert statistics
function updateAlertStatistics() {
    const stats = {
        new: alertsData.filter(alert => alert.status === 'new').length,
        reviewed: alertsData.filter(alert => alert.status === 'reviewed').length,
        dismissed: alertsData.filter(alert => alert.status === 'dismissed').length,
        total: alertsData.length
    };

    newAlertsCount.textContent = stats.new;
    reviewedAlertsCount.textContent = stats.reviewed;
    dismissedAlertsCount.textContent = stats.dismissed;
    totalAlertsCount.textContent = stats.total;
}

// Alert filtering functions
function applyAlertFilters() {
    const filters = {
        status: statusFilter.value,
        dateFrom: dateFromFilter.value,
        dateTo: dateToFilter.value,
        customer: customerFilter.value.trim().toLowerCase()
    };

    currentAlertFilters = filters;

    filteredAlerts = alertsData.filter(alert => {
        // Status filter
        if (filters.status !== 'all' && alert.status !== filters.status) {
            return false;
        }

        // Date range filter - handle both single date and date range alerts
        const alertStartDate = alert.startDate || alert.date;
        const alertEndDate = alert.endDate || alert.date;

        if (filters.dateFrom && alertEndDate < filters.dateFrom) {
            return false;
        }
        if (filters.dateTo && alertStartDate > filters.dateTo) {
            return false;
        }

        // Customer filter
        if (filters.customer && !alert.customerId.toLowerCase().includes(filters.customer)) {
            return false;
        }

        return true;
    });

    currentAlertPage = 1;
    displayAlerts();
    updateAlertPagination();
}

function clearAlertFilters() {
    statusFilter.value = 'all';
    dateFromFilter.value = '';
    dateToFilter.value = '';
    customerFilter.value = '';
    applyAlertFilters();
}

// Display alerts
function displayAlerts() {
    const startIndex = (currentAlertPage - 1) * alertsPerPage;
    const endIndex = startIndex + alertsPerPage;
    const pageAlerts = filteredAlerts.slice(startIndex, endIndex);

    alertsContainer.innerHTML = '';

    if (pageAlerts.length === 0) {
        alertsContainer.appendChild(noAlertsMessage);
        alertPagination.style.display = 'none';
        bulkActions.style.display = 'none';
        return;
    }

    noAlertsMessage.style.display = 'none';
    alertPagination.style.display = 'flex';

    pageAlerts.forEach(alert => {
        const alertElement = createAlertElement(alert);
        alertsContainer.appendChild(alertElement);
    });

    updateBulkActionsVisibility();
}

// Create alert element
function createAlertElement(alert) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert-item';
    alertDiv.dataset.alertId = alert.id;

    alertDiv.innerHTML = `
        <div class="alert-item-header">
            <div class="alert-type">
                <i class="fas fa-exclamation-triangle"></i>
                ${alert.title}
            </div>
            <div class="alert-status ${alert.status}">${alert.status}</div>
        </div>
        <div class="alert-item-body">
            <div class="alert-info-grid">
                <div class="alert-info-item">
                    <div class="alert-info-label">Customer</div>
                    <div class="alert-info-value">${alert.customerId} - ${alert.customerName}</div>
                </div>
                <div class="alert-info-item">
                    <div class="alert-info-label">Date Range</div>
                    <div class="alert-info-value">${alert.dateRange || alert.date || 'N/A'}</div>
                </div>
                <div class="alert-info-item">
                    <div class="alert-info-label">Transaction Pairs</div>
                    <div class="alert-info-value">${alert.pairCount || 1} pair${(alert.pairCount || 1) > 1 ? 's' : ''}</div>
                </div>
                <div class="alert-info-item">
                    <div class="alert-info-label">Total Amount</div>
                    <div class="alert-info-value">${formatCurrency(alert.totalAmount || alert.amount || 0)}</div>
                </div>
                <div class="alert-info-item">
                    <div class="alert-info-label">Severity</div>
                    <div class="alert-info-value text-warning">${alert.severity.toUpperCase()}</div>
                </div>
            </div>
            <div class="alert-actions-row">
                <div class="alert-timestamp">
                    <i class="fas fa-clock"></i> ${formatTimestamp(alert.timestamp)}
                </div>
                <div class="alert-item-actions">
                    <input type="checkbox" class="alert-checkbox" data-alert-id="${alert.id}">
                    <button class="btn btn-sm btn-primary view-details-btn" data-alert-id="${alert.id}">
                        <i class="fas fa-eye"></i> View Details
                    </button>
                    <button class="btn btn-sm btn-secondary add-note-btn" data-alert-id="${alert.id}">
                        <i class="fas fa-sticky-note"></i> Add Note
                    </button>
                    ${alert.status === 'new' ?
                        `<button class="btn btn-sm btn-outline review-btn" data-alert-id="${alert.id}">
                            <i class="fas fa-eye"></i> Review
                        </button>` : ''
                    }
                    ${alert.status !== 'dismissed' ?
                        `<button class="btn btn-sm btn-outline dismiss-btn" data-alert-id="${alert.id}">
                            <i class="fas fa-times"></i> Dismiss
                        </button>` : ''
                    }
                </div>
            </div>
        </div>
    `;

    // Add click event for selection
    const checkbox = alertDiv.querySelector('.alert-checkbox');
    checkbox.addEventListener('change', (e) => {
        if (e.target.checked) {
            selectedAlerts.add(alert.id);
            alertDiv.classList.add('selected');
        } else {
            selectedAlerts.delete(alert.id);
            alertDiv.classList.remove('selected');
        }
        updateBulkActionsVisibility();
    });

    // Add event listeners for action buttons
    const viewDetailsBtn = alertDiv.querySelector('.view-details-btn');
    if (viewDetailsBtn) {
        viewDetailsBtn.addEventListener('click', () => viewAlertDetails(alert.id));
    }

    const addNoteBtn = alertDiv.querySelector('.add-note-btn');
    if (addNoteBtn) {
        addNoteBtn.addEventListener('click', () => addAlertNote(alert.id));
    }

    const reviewBtn = alertDiv.querySelector('.review-btn');
    if (reviewBtn) {
        reviewBtn.addEventListener('click', () => updateAlertStatus(alert.id, 'reviewed'));
    }

    const dismissBtn = alertDiv.querySelector('.dismiss-btn');
    if (dismissBtn) {
        dismissBtn.addEventListener('click', () => updateAlertStatus(alert.id, 'dismissed'));
    }

    return alertDiv;
}

// Format timestamp for display
function formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString();
}

// Update bulk actions visibility
function updateBulkActionsVisibility() {
    if (selectedAlerts.size > 0) {
        bulkActions.style.display = 'flex';
    } else {
        bulkActions.style.display = 'none';
    }
}

// Alert pagination
function updateAlertPagination() {
    const totalPages = Math.ceil(filteredAlerts.length / alertsPerPage);

    alertPageInfo.textContent = `Page ${currentAlertPage} of ${totalPages}`;
    alertPrevBtn.disabled = currentAlertPage <= 1;
    alertNextBtn.disabled = currentAlertPage >= totalPages;
}

function changeAlertPage(direction) {
    const totalPages = Math.ceil(filteredAlerts.length / alertsPerPage);
    const newPage = currentAlertPage + direction;

    if (newPage >= 1 && newPage <= totalPages) {
        currentAlertPage = newPage;
        displayAlerts();
        updateAlertPagination();
    }
}

// Alert management functions
function updateAlertStatus(alertId, newStatus) {
    const alert = alertsData.find(a => a.id === alertId);
    if (alert) {
        alert.status = newStatus;
        updateAlertStatistics();
        updateAlertBadge();
        applyAlertFilters(); // Refresh display
    }
}

function bulkUpdateAlerts(newStatus) {
    if (selectedAlerts.size === 0) return;

    selectedAlerts.forEach(alertId => {
        updateAlertStatus(alertId, newStatus);
    });

    selectedAlerts.clear();
    updateBulkActionsVisibility();
}

// Alert detail modal functions
let currentAlertId = null;

function viewAlertDetails(alertId) {
    const alert = alertsData.find(a => a.id === alertId);
    if (!alert) return;

    currentAlertId = alertId;

    const detailContent = `
        <div class="alert-detail-section">
            <h4><i class="fas fa-info-circle"></i> Alert Information</h4>
            <div class="alert-info-grid">
                <div class="alert-info-item">
                    <div class="alert-info-label">Alert Type</div>
                    <div class="alert-info-value">${alert.title}</div>
                </div>
                <div class="alert-info-item">
                    <div class="alert-info-label">Description</div>
                    <div class="alert-info-value">${alert.description}</div>
                </div>
                <div class="alert-info-item">
                    <div class="alert-info-label">Severity</div>
                    <div class="alert-info-value text-warning">${alert.severity.toUpperCase()}</div>
                </div>
                <div class="alert-info-item">
                    <div class="alert-info-label">Status</div>
                    <div class="alert-info-value alert-status ${alert.status}">${alert.status}</div>
                </div>
                <div class="alert-info-item">
                    <div class="alert-info-label">Customer ID</div>
                    <div class="alert-info-value">${alert.customerId}</div>
                </div>
                <div class="alert-info-item">
                    <div class="alert-info-label">Customer Name</div>
                    <div class="alert-info-value">${alert.customerName}</div>
                </div>
                <div class="alert-info-item">
                    <div class="alert-info-label">Date Range</div>
                    <div class="alert-info-value">${alert.dateRange || alert.date || 'N/A'}</div>
                </div>
                <div class="alert-info-item">
                    <div class="alert-info-label">Transaction Pairs</div>
                    <div class="alert-info-value">${alert.pairCount || 1} pair${(alert.pairCount || 1) > 1 ? 's' : ''}</div>
                </div>
                <div class="alert-info-item">
                    <div class="alert-info-label">Total Amount</div>
                    <div class="alert-info-value">${formatCurrency(alert.totalAmount || alert.amount || 0)}</div>
                </div>
            </div>
        </div>

        <div class="alert-detail-section">
            <h4><i class="fas fa-exchange-alt"></i> Transaction Details</h4>
            ${generateTransactionPairsHTML(alert)}
        </div>

        ${alert.notes.length > 0 ? `
        <div class="alert-detail-section">
            <h4><i class="fas fa-sticky-note"></i> Notes</h4>
            <div class="alert-notes">
                ${alert.notes.map(note => `
                    <div class="alert-note">
                        <div class="alert-note-content">${note.content}</div>
                        <div class="alert-note-timestamp">${formatTimestamp(note.timestamp)}</div>
                    </div>
                `).join('')}
            </div>
        </div>
        ` : ''}
    `;

    alertDetailContent.innerHTML = detailContent;
    alertDetailModal.style.display = 'flex';
}

function closeAlertDetailModal() {
    alertDetailModal.style.display = 'none';
    currentAlertId = null;
}

function updateCurrentAlertStatus(newStatus) {
    if (currentAlertId) {
        updateAlertStatus(currentAlertId, newStatus);
        closeAlertDetailModal();
    }
}

// Generate HTML for transaction pairs
function generateTransactionPairsHTML(alert) {
    // Handle both old format (single pair) and new format (multiple pairs)
    let pairs = [];

    if (alert.transactionPairs && alert.transactionPairs.length > 0) {
        // New consolidated format
        pairs = alert.transactionPairs;
    } else if (alert.debitTransaction && alert.creditTransaction) {
        // Old format - single pair
        pairs = [{
            debitTransaction: alert.debitTransaction,
            creditTransaction: alert.creditTransaction,
            amount: alert.amount
        }];
    }

    if (pairs.length === 0) {
        return '<p>No transaction details available.</p>';
    }

    return pairs.map((pair, index) => `
        <div class="transaction-pair-section">
            <h5><i class="fas fa-exchange-alt"></i> Transaction Pair ${pairs.length > 1 ? (index + 1) : ''} - ${formatCurrency(pair.amount)} - Counter Party: ${pair.counterParty || pair.debitTransaction['Counter Party Name'] || 'N/A'}</h5>
            <div class="transaction-details-grid">
                <div class="transaction-detail-card debit">
                    <div class="transaction-detail-title">
                        <i class="fas fa-arrow-down text-danger"></i>
                        Debit Transaction
                    </div>
                    <div class="alert-info-grid">
                        <div class="alert-info-item">
                            <div class="alert-info-label">Transaction ID</div>
                            <div class="alert-info-value">${pair.debitTransaction['Transaction ID'] || 'N/A'}</div>
                        </div>
                        <div class="alert-info-item">
                            <div class="alert-info-label">Reference No</div>
                            <div class="alert-info-value">${pair.debitTransaction['Trans Ref No'] || 'N/A'}</div>
                        </div>
                        <div class="alert-info-item">
                            <div class="alert-info-label">Account No</div>
                            <div class="alert-info-value">${pair.debitTransaction['Account No'] || 'N/A'}</div>
                        </div>
                        <div class="alert-info-item">
                            <div class="alert-info-label">Counter Party</div>
                            <div class="alert-info-value">${pair.debitTransaction['Counter Party Name'] || 'N/A'}</div>
                        </div>
                        <div class="alert-info-item">
                            <div class="alert-info-label">Remarks</div>
                            <div class="alert-info-value">${pair.debitTransaction['Remarks'] || 'N/A'}</div>
                        </div>
                    </div>
                </div>

                <div class="transaction-detail-card credit">
                    <div class="transaction-detail-title">
                        <i class="fas fa-arrow-up text-success"></i>
                        Credit Transaction
                    </div>
                    <div class="alert-info-grid">
                        <div class="alert-info-item">
                            <div class="alert-info-label">Transaction ID</div>
                            <div class="alert-info-value">${pair.creditTransaction['Transaction ID'] || 'N/A'}</div>
                        </div>
                        <div class="alert-info-item">
                            <div class="alert-info-label">Reference No</div>
                            <div class="alert-info-value">${pair.creditTransaction['Trans Ref No'] || 'N/A'}</div>
                        </div>
                        <div class="alert-info-item">
                            <div class="alert-info-label">Account No</div>
                            <div class="alert-info-value">${pair.creditTransaction['Account No'] || 'N/A'}</div>
                        </div>
                        <div class="alert-info-item">
                            <div class="alert-info-label">Counter Party</div>
                            <div class="alert-info-value">${pair.creditTransaction['Counter Party Name'] || 'N/A'}</div>
                        </div>
                        <div class="alert-info-item">
                            <div class="alert-info-label">Remarks</div>
                            <div class="alert-info-value">${pair.creditTransaction['Remarks'] || 'N/A'}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

// Alert notes functions
let currentNoteAlertId = null;

function addAlertNote(alertId) {
    currentNoteAlertId = alertId;
    alertNoteText.value = '';
    alertNotesModal.style.display = 'flex';
}

function closeAlertNotesModal() {
    alertNotesModal.style.display = 'none';
    currentNoteAlertId = null;
    alertNoteText.value = '';
}

function saveAlertNote() {
    if (!currentNoteAlertId || !alertNoteText.value.trim()) return;

    const alert = alertsData.find(a => a.id === currentNoteAlertId);
    if (alert) {
        alert.notes.push({
            content: alertNoteText.value.trim(),
            timestamp: new Date().toISOString()
        });

        closeAlertNotesModal();
        applyAlertFilters(); // Refresh display
    }
}

// Export alerts function
function exportAlerts() {
    if (filteredAlerts.length === 0) {
        showError('No alerts to export');
        return;
    }

    try {
        // Create new workbook
        const wb = XLSX.utils.book_new();

        // Prepare alert data for export
        const alertExportData = [
            [
                'Alert ID',
                'Alert Type',
                'Description',
                'Status',
                'Severity',
                'Customer ID',
                'Customer Name',
                'Date Range',
                'Total Amount',
                'Transaction Pairs Count',
                'Transaction Details',
                'Alert Timestamp',
                'Notes'
            ]
        ];

        filteredAlerts.forEach(alert => {
            const notes = alert.notes.map(note => `${formatTimestamp(note.timestamp)}: ${note.content}`).join(' | ');

            // Handle both consolidated and legacy alert formats
            if (alert.transactionPairs && alert.transactionPairs.length > 0) {
                // Consolidated format - create one row with summary and detailed breakdown
                const transactionDetails = alert.transactionPairs.map((pair, index) =>
                    `Pair ${index + 1}: Dr(${pair.debitTransaction['Transaction ID']}-${pair.debitTransaction['Trans Ref No']}) Cr(${pair.creditTransaction['Transaction ID']}-${pair.creditTransaction['Trans Ref No']}) Amount:${formatCurrency(pair.amount)} CounterParty:${pair.counterParty || pair.debitTransaction['Counter Party Name'] || 'N/A'}`
                ).join(' | ');

                alertExportData.push([
                    alert.id,
                    alert.title,
                    alert.description,
                    alert.status,
                    alert.severity,
                    alert.customerId,
                    alert.customerName,
                    alert.dateRange || alert.date || 'N/A',
                    alert.totalAmount || alert.amount || 0,
                    alert.pairCount || 1,
                    transactionDetails,
                    formatTimestamp(alert.timestamp),
                    notes
                ]);
            } else {
                // Legacy format - single pair
                alertExportData.push([
                    alert.id,
                    alert.title,
                    alert.description,
                    alert.status,
                    alert.severity,
                    alert.customerId,
                    alert.customerName,
                    alert.dateRange || alert.date || 'N/A',
                    alert.amount || 0,
                    1,
                    `Dr(${alert.debitTransaction?.['Transaction ID'] || 'N/A'}-${alert.debitTransaction?.['Trans Ref No'] || 'N/A'}) Cr(${alert.creditTransaction?.['Transaction ID'] || 'N/A'}-${alert.creditTransaction?.['Trans Ref No'] || 'N/A'}) CounterParty:${alert.debitTransaction?.['Counter Party Name'] || 'N/A'}`,
                    formatTimestamp(alert.timestamp),
                    notes
                ]);
            }
        });

        const ws = XLSX.utils.aoa_to_sheet(alertExportData);

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Alerts');

        // Generate filename with timestamp
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const filename = `transaction_alerts_${timestamp}.xlsx`;

        // Save file
        XLSX.writeFile(wb, filename);

        showUploadStatus(`Alerts exported successfully as ${filename}`, 'success');

    } catch (error) {
        showError('Error exporting alerts: ' + error.message);
    }
}

// Clear all alerts function
function clearAllAlerts() {
    if (alertsData.length === 0) {
        showError('No alerts to clear');
        return;
    }

    if (confirm('Are you sure you want to clear all alerts? This action cannot be undone.')) {
        alertsData = [];
        filteredAlerts = [];
        selectedAlerts.clear();
        currentAlertPage = 1;

        updateAlertStatistics();
        updateAlertBadge();
        displayAlerts();
        updateAlertPagination();

        showUploadStatus('All alerts cleared successfully', 'success');
    }
}

// Update clear data function to also clear alerts
const originalClearData = clearData;
function clearData() {
    if (confirm('Are you sure you want to clear all data and alerts? This action cannot be undone.')) {
        // Clear alerts
        alertsData = [];
        filteredAlerts = [];
        selectedAlerts.clear();
        currentAlertPage = 1;
        updateAlertStatistics();
        updateAlertBadge();

        // Clear transaction data
        transactionData = [];
        currentPage = 1;
        dataTableBody.innerHTML = '';
        hideSections();
        showUploadStatus('', '');
        fileInput.value = '';

        // Switch back to transactions view
        switchView('transactions');
    }
}
