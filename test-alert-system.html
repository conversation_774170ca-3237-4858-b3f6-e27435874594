<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Alert System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
        }
        .test-result {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <h1>Alert System Test</h1>
    
    <div class="test-section">
        <h2>Test Alert Generation</h2>
        <p>This test creates sample transaction data and verifies that the alert system correctly identifies same-day debit/credit patterns.</p>
        <button onclick="runAlertTest()">Run Alert Test</button>
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>Test Data</h2>
        <p>Sample transactions that should generate alerts:</p>
        <ul>
            <li>CUST001: $1000 Dr and $1000 Cr on 2024-01-15</li>
            <li>CUST002: $500 Dr and $500 Cr on 2024-01-16</li>
            <li>CUST003: $2000 Dr and $2000 Cr on 2024-01-17</li>
            <li>CUST001: $250 Dr and $250 Cr on 2024-01-20</li>
        </ul>
        <p>Expected: 4 alerts should be generated</p>
    </div>

    <script>
        // Simplified alert generation logic for testing
        function runAlertTest() {
            const testData = [
                {
                    'Transaction ID': 'TXN001',
                    'Trans Ref No': 'REF001',
                    'Customer Id': 'CUST001',
                    'Customer Name': 'John Smith',
                    'Account No': 'ACC001',
                    'Date': '2024-01-15',
                    'Tran Amount': 1000.00,
                    'Dr or Cr': 'Dr'
                },
                {
                    'Transaction ID': 'TXN002',
                    'Trans Ref No': 'REF002',
                    'Customer Id': 'CUST001',
                    'Customer Name': 'John Smith',
                    'Account No': 'ACC001',
                    'Date': '2024-01-15',
                    'Tran Amount': 1000.00,
                    'Dr or Cr': 'Cr'
                },
                {
                    'Transaction ID': 'TXN003',
                    'Trans Ref No': 'REF003',
                    'Customer Id': 'CUST002',
                    'Customer Name': 'Jane Doe',
                    'Account No': 'ACC004',
                    'Date': '2024-01-16',
                    'Tran Amount': 500.00,
                    'Dr or Cr': 'Dr'
                },
                {
                    'Transaction ID': 'TXN004',
                    'Trans Ref No': 'REF004',
                    'Customer Id': 'CUST002',
                    'Customer Name': 'Jane Doe',
                    'Account No': 'ACC004',
                    'Date': '2024-01-16',
                    'Tran Amount': 500.00,
                    'Dr or Cr': 'Cr'
                },
                {
                    'Transaction ID': 'TXN005',
                    'Trans Ref No': 'REF005',
                    'Customer Id': 'CUST003',
                    'Customer Name': 'Bob Johnson',
                    'Account No': 'ACC007',
                    'Date': '2024-01-17',
                    'Tran Amount': 2000.00,
                    'Dr or Cr': 'Dr'
                },
                {
                    'Transaction ID': 'TXN006',
                    'Trans Ref No': 'REF006',
                    'Customer Id': 'CUST003',
                    'Customer Name': 'Bob Johnson',
                    'Account No': 'ACC007',
                    'Date': '2024-01-17',
                    'Tran Amount': 2000.00,
                    'Dr or Cr': 'Cr'
                },
                {
                    'Transaction ID': 'TXN009',
                    'Trans Ref No': 'REF009',
                    'Customer Id': 'CUST001',
                    'Customer Name': 'John Smith',
                    'Account No': 'ACC001',
                    'Date': '2024-01-20',
                    'Tran Amount': 250.00,
                    'Dr or Cr': 'Dr'
                },
                {
                    'Transaction ID': 'TXN010',
                    'Trans Ref No': 'REF010',
                    'Customer Id': 'CUST001',
                    'Customer Name': 'John Smith',
                    'Account No': 'ACC001',
                    'Date': '2024-01-20',
                    'Tran Amount': 250.00,
                    'Dr or Cr': 'Cr'
                }
            ];

            const alerts = generateTestAlerts(testData);
            displayTestResults(alerts);
        }

        function generateTestAlerts(transactionData) {
            const alerts = [];
            
            // Group transactions by customer and date
            const groups = {};
            
            transactionData.forEach((transaction, index) => {
                const customerId = transaction['Customer Id'];
                const date = transaction['Date'];
                const amount = transaction['Tran Amount'];
                const drCr = transaction['Dr or Cr'];
                
                const key = `${customerId}_${date}`;
                
                if (!groups[key]) {
                    groups[key] = {
                        customerId,
                        date,
                        transactions: []
                    };
                }
                
                groups[key].transactions.push({
                    ...transaction,
                    originalIndex: index,
                    amount,
                    drCr
                });
            });
            
            // Detect same-day debit/credit alerts
            Object.values(groups).forEach(group => {
                const { customerId, date, transactions } = group;
                
                // Group by amount
                const amountGroups = {};
                transactions.forEach(transaction => {
                    const amount = transaction.amount;
                    if (!amountGroups[amount]) {
                        amountGroups[amount] = { debits: [], credits: [] };
                    }
                    
                    if (transaction.drCr === 'Dr') {
                        amountGroups[amount].debits.push(transaction);
                    } else if (transaction.drCr === 'Cr') {
                        amountGroups[amount].credits.push(transaction);
                    }
                });
                
                // Check for matching debit/credit pairs
                Object.entries(amountGroups).forEach(([amount, { debits, credits }]) => {
                    if (debits.length > 0 && credits.length > 0) {
                        // Create alert for each debit/credit pair
                        debits.forEach(debit => {
                            credits.forEach(credit => {
                                alerts.push({
                                    id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                                    type: 'same_day_debit_credit',
                                    title: 'Same-Day Debit/Credit Alert',
                                    description: `Customer has both debit and credit transactions of $${amount} on the same day`,
                                    customerId: customerId,
                                    customerName: debit['Customer Name'],
                                    date: date,
                                    amount: parseFloat(amount),
                                    debitTransaction: debit,
                                    creditTransaction: credit,
                                    status: 'new',
                                    timestamp: new Date().toISOString()
                                });
                            });
                        });
                    }
                });
            });
            
            return alerts;
        }

        function displayTestResults(alerts) {
            const resultsDiv = document.getElementById('testResults');
            
            if (alerts.length === 4) {
                resultsDiv.innerHTML = `
                    <div class="test-result">
                        <strong>✅ Test PASSED!</strong><br>
                        Generated ${alerts.length} alerts as expected.<br><br>
                        <strong>Alert Details:</strong><br>
                        ${alerts.map(alert => 
                            `• ${alert.customerId} - ${alert.customerName}: $${alert.amount} on ${alert.date}`
                        ).join('<br>')}
                    </div>
                `;
            } else {
                resultsDiv.innerHTML = `
                    <div class="test-error">
                        <strong>❌ Test FAILED!</strong><br>
                        Expected 4 alerts, but generated ${alerts.length} alerts.<br><br>
                        <strong>Generated Alerts:</strong><br>
                        ${alerts.map(alert => 
                            `• ${alert.customerId} - ${alert.customerName}: $${alert.amount} on ${alert.date}`
                        ).join('<br>')}
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
